import time: self [us] | cumulative | imported package
import time:       184 |        184 |   _io
import time:        34 |         34 |   marshal
import time:       247 |        247 |   posix
import time:       781 |       1244 | _frozen_importlib_external
import time:        69 |         69 |   time
import time:       204 |        273 | zipimport
import time:        35 |         35 |     _codecs
import time:       414 |        449 |   codecs
import time:       287 |        287 |   encodings.aliases
import time:       393 |       1129 | encodings
import time:       467 |        467 | encodings.utf_8
import time:        77 |         77 | _signal
import time:        29 |         29 |     _abc
import time:       142 |        170 |   abc
import time:       145 |        315 | io
import time:        41 |         41 |       _stat
import time:        83 |        124 |     stat
import time:      1562 |       1562 |     _collections_abc
import time:       163 |        163 |       errno
import time:       240 |        240 |       genericpath
import time:      3426 |       3828 |     posixpath
import time:       786 |       6300 |   os
import time:        86 |         86 |   _sitebuiltins
import time:       373 |        373 |   encodings.utf_8_sig
import time:       351 |        351 |   _distutils_hack
import time:       117 |        117 |   sitecustomize
import time:       791 |       8015 | site
import time:       359 |        359 | linecache
import time:        96 |         96 |   __future__
import time:       162 |        162 |               itertools
import time:       147 |        147 |               keyword
import time:       162 |        162 |                 _operator
import time:       632 |        793 |               operator
import time:       256 |        256 |               reprlib
import time:        96 |         96 |               _collections
import time:      1776 |       3228 |             collections
import time:       127 |        127 |             _functools
import time:      1150 |       4503 |           functools
import time:       189 |        189 |           types
import time:      1503 |       6194 |         enum
import time:        62 |         62 |           _sre
import time:       166 |        166 |             re._constants
import time:       279 |        444 |           re._parser
import time:        99 |         99 |           re._casefix
import time:       292 |        895 |         re._compiler
import time:       112 |        112 |         copyreg
import time:       462 |       7661 |       re
import time:       223 |        223 |           _weakrefset
import time:       842 |       1065 |         weakref
import time:       125 |       1189 |       copy
import time:       837 |        837 |           _ast
import time:       454 |        454 |           contextlib
import time:       838 |       2127 |         ast
import time:       187 |        187 |             _opcode
import time:       150 |        150 |             _opcode_metadata
import time:       188 |        524 |           opcode
import time:       622 |       1145 |         dis
import time:       102 |        102 |           importlib
import time:        52 |        154 |         importlib.machinery
import time:       456 |        456 |           token
import time:        36 |         36 |           _tokenize
import time:      1624 |       2115 |         tokenize
import time:      1667 |       7208 |       inspect
import time:       519 |      16576 |     dataclasses
import time:        47 |         47 |       _typing
import time:      1832 |       1879 |     typing
import time:       230 |        230 |       warnings
import time:       122 |        122 |       numpy.version
import time:        76 |         76 |       numpy._expired_attrs_2_0
import time:        82 |         82 |           numpy._utils._convertions
import time:        83 |        164 |         numpy._utils
import time:       407 |        570 |       numpy._globals
import time:        21 |         21 |         numpy._distributor_init_local
import time:        90 |        110 |       numpy._distributor_init
import time:       301 |        301 |                   _datetime
import time:       196 |        496 |                 datetime
import time:       176 |        176 |                 math
import time:       176 |        176 |                 numpy.exceptions
import time:       451 |        451 |                 numpy._core._exceptions
import time:       122 |        122 |                     _contextvars
import time:        66 |        187 |                   contextvars
import time:       104 |        290 |                 numpy._core.printoptions
import time:       231 |        231 |                 numpy.dtypes
import time:      4904 |       6722 |               numpy._core._multiarray_umath
import time:       129 |        129 |                 numpy._utils._inspect
import time:       329 |        458 |               numpy._core.overrides
import time:       548 |       7726 |             numpy._core.multiarray
import time:       142 |        142 |             numpy._core.umath
import time:       439 |        439 |               numbers
import time:       325 |        325 |               numpy._core._dtype
import time:       161 |        161 |               numpy._core._string_helpers
import time:       936 |        936 |               numpy._core._type_aliases
import time:      1022 |       2881 |             numpy._core.numerictypes
import time:       325 |        325 |               numpy._core._ufunc_config
import time:       295 |        295 |                       _struct
import time:       262 |        557 |                     struct
import time:       415 |        415 |                     _compat_pickle
import time:       330 |        330 |                     _pickle
import time:      1384 |       2684 |                   pickle
import time:       376 |       3060 |                 numpy._core._methods
import time:      3706 |       6765 |               numpy._core.fromnumeric
import time:       522 |       7612 |             numpy._core._machar
import time:       463 |        463 |                 numpy._core.shape_base
import time:       216 |        216 |                 numpy._core._asarray
import time:      1309 |       1309 |                 numpy._core.arrayprint
import time:      1544 |       3531 |               numpy._core.numeric
import time:       410 |       3940 |             numpy._core.einsumfunc
import time:       305 |        305 |             numpy._core.function_base
import time:       718 |        718 |             numpy._core.getlimits
import time:       151 |        151 |             numpy._core.memmap
import time:       238 |        238 |             numpy._core.records
import time:       959 |        959 |             numpy._core._add_newdocs
import time:       288 |        288 |             numpy._core._add_newdocs_scalars
import time:        93 |         93 |             numpy._core._dtype_ctypes
import time:       456 |        456 |                 _ctypes
import time:       233 |        233 |                 ctypes._endian
import time:       814 |       1502 |               ctypes
import time:       757 |       2258 |             numpy._core._internal
import time:       158 |        158 |             numpy._pytesttester
import time:       952 |      28414 |           numpy._core
import time:        43 |      28456 |         numpy._core._multiarray_umath
import time:       241 |      28697 |       numpy.__config__
import time:       173 |        173 |                         numpy._typing._nbit_base
import time:       207 |        207 |                         numpy._typing._nested_sequence
import time:        83 |         83 |                         numpy._typing._shape
import time:      1572 |       2033 |                       numpy._typing._array_like
import time:      1011 |       1011 |                       numpy._typing._char_codes
import time:      3307 |       3307 |                       numpy._typing._dtype_like
import time:       125 |        125 |                       numpy._typing._nbit
import time:        99 |         99 |                       numpy._typing._scalars
import time:        71 |         71 |                       numpy._typing._ufunc
import time:       864 |       7507 |                     numpy._typing
import time:       181 |        181 |                       numpy.lib._stride_tricks_impl
import time:       372 |        552 |                     numpy.lib._twodim_base_impl
import time:        68 |         68 |                       numpy.lib._array_utils_impl
import time:       116 |        184 |                     numpy.lib.array_utils
import time:       358 |        358 |                     numpy.linalg._umath_linalg
import time:      1610 |      10210 |                   numpy.linalg._linalg
import time:        99 |         99 |                   numpy.linalg.linalg
import time:       233 |      10541 |                 numpy.linalg
import time:       248 |      10788 |               numpy.matrixlib.defmatrix
import time:       585 |      11373 |             numpy.matrixlib
import time:       206 |        206 |               numpy.lib._histograms_impl
import time:      1104 |       1309 |             numpy.lib._function_base_impl
import time:        96 |         96 |             numpy.lib.stride_tricks
import time:       472 |      13249 |           numpy.lib._index_tricks_impl
import time:       258 |      13506 |         numpy.lib._arraypad_impl
import time:       520 |        520 |         numpy.lib._arraysetops_impl
import time:       116 |        116 |         numpy.lib._arrayterator_impl
import time:       357 |        357 |         numpy.lib._nanfunctions_impl
import time:        99 |         99 |                   _wmi
import time:       546 |        645 |                 platform
import time:       868 |        868 |                 textwrap
import time:       305 |       1818 |               numpy.lib._utils_impl
import time:       171 |       1989 |             numpy.lib._format_impl
import time:        94 |       2082 |           numpy.lib.format
import time:       219 |        219 |           numpy.lib._datasource
import time:       275 |        275 |           numpy.lib._iotools
import time:       588 |       3164 |         numpy.lib._npyio_impl
import time:        82 |         82 |             numpy.lib._ufunclike_impl
import time:       268 |        349 |           numpy.lib._type_check_impl
import time:       445 |        794 |         numpy.lib._polynomial_impl
import time:       257 |        257 |         numpy.lib._shape_base_impl
import time:       165 |        165 |         numpy.lib._version
import time:        55 |         55 |         numpy.lib.introspect
import time:       137 |        137 |         numpy.lib.mixins
import time:        58 |         58 |         numpy.lib.npyio
import time:       166 |        166 |           numpy.lib._scimath_impl
import time:        51 |        217 |         numpy.lib.scimath
import time:       489 |      19828 |       numpy.lib
import time:        92 |         92 |       numpy._array_api_info
import time:      1705 |      51425 |     numpy
import time:       173 |        173 |         pytz.exceptions
import time:       660 |        660 |           threading
import time:       242 |        901 |         pytz.lazy
import time:       863 |        863 |             _bisect
import time:       197 |       1059 |           bisect
import time:       299 |       1358 |         pytz.tzinfo
import time:       176 |        176 |         pytz.tzfile
import time:       840 |       3446 |       pytz
import time:       187 |        187 |         dateutil._version
import time:       288 |        474 |       dateutil
import time:       253 |        253 |           sysconfig
import time:       725 |        725 |           _sysconfigdata__linux_x86_64-linux-gnu
import time:      1191 |       2168 |         pandas.compat._constants
import time:       237 |        237 |             _compression
import time:       365 |        365 |             _bz2
import time:       531 |       1131 |           bz2
import time:       727 |        727 |             _lzma
import time:       388 |       1114 |           lzma
import time:       384 |       2629 |         pandas.compat.compressors
import time:       119 |        119 |             pandas.util
import time:      2640 |       2758 |           pandas.util.version
import time:       560 |       3318 |         pandas.compat.numpy
import time:        85 |         85 |           pyarrow
import time:       308 |        392 |         pandas.compat.pyarrow
import time:       977 |       9482 |       pandas.compat
import time:       591 |        591 |                   numpy.random._common
import time:       282 |        282 |                       binascii
import time:       401 |        683 |                     base64
import time:      2198 |       2198 |                       _hashlib
import time:       238 |        238 |                         _blake2
import time:       379 |        617 |                       hashlib
import time:       481 |       3295 |                     hmac
import time:       212 |        212 |                       _random
import time:       753 |        964 |                     random
import time:       448 |       5388 |                   secrets
import time:       903 |       6882 |                 numpy.random.bit_generator
import time:       446 |       7327 |               numpy.random._bounded_integers
import time:       398 |        398 |                   numpy.random._pcg64
import time:       328 |        328 |                   numpy.random._mt19937
import time:      2152 |       2877 |                 numpy.random._generator
import time:       366 |        366 |                 numpy.random._philox
import time:       264 |        264 |                 numpy.random._sfc64
import time:      1194 |       1194 |                 numpy.random.mtrand
import time:       530 |       5229 |               numpy.random._pickle
import time:       419 |      12974 |             numpy.random
import time:      4442 |      17416 |           pandas._typing
import time:       276 |        276 |           pandas.util._exceptions
import time:      1345 |      19037 |         pandas._config.config
import time:       622 |        622 |         pandas._config.dates
import time:       131 |        131 |             _locale
import time:      1332 |       1463 |           locale
import time:       331 |       1793 |         pandas._config.display
import time:       544 |      21994 |       pandas._config
import time:       185 |        185 |         pandas.core
import time:      1290 |       1474 |       pandas.core.config_init
import time:       284 |        284 |           pandas._libs.pandas_parser
import time:       134 |        134 |           pandas._libs.pandas_datetime
import time:       144 |        144 |                       pandas._libs.tslibs.ccalendar
import time:       233 |        233 |                       pandas._libs.tslibs.np_datetime
import time:       854 |       1230 |                     pandas._libs.tslibs.dtypes
import time:       145 |        145 |                       pandas._libs.tslibs.base
import time:       284 |        284 |                           pandas._libs.tslibs.nattype
import time:       396 |        396 |                               pandas.compat._optional
import time:       447 |        447 |                                 zoneinfo._tzpath
import time:       228 |        228 |                                 zoneinfo._common
import time:       364 |        364 |                                 _zoneinfo
import time:       648 |       1686 |                               zoneinfo
import time:       224 |        224 |                                       importlib._abc
import time:       256 |        480 |                                     importlib.util
import time:       821 |       1300 |                                   six
import time:        39 |         39 |                                   six.moves
import time:       203 |        203 |                                   dateutil.tz._common
import time:       141 |        141 |                                   dateutil.tz._factories
import time:        25 |         25 |                                     six.moves.winreg
import time:       389 |        414 |                                   dateutil.tz.win
import time:      1225 |       3318 |                                 dateutil.tz.tz
import time:       300 |       3618 |                               dateutil.tz
import time:      1106 |       6804 |                             pandas._libs.tslibs.timezones
import time:      1758 |       1758 |                                 calendar
import time:      1219 |       2976 |                               _strptime
import time:       444 |        444 |                                   signal
import time:       187 |        187 |                                   fcntl
import time:        61 |         61 |                                   msvcrt
import time:       113 |        113 |                                   _posixsubprocess
import time:       154 |        154 |                                   select
import time:       409 |        409 |                                   selectors
import time:       843 |       2207 |                                 subprocess
import time:       229 |       2436 |                               pandas._config.localization
import time:       953 |       6364 |                             pandas._libs.tslibs.fields
import time:      1169 |      14336 |                           pandas._libs.tslibs.timedeltas
import time:       366 |        366 |                           pandas._libs.tslibs.tzconversion
import time:       707 |      15692 |                         pandas._libs.tslibs.timestamps
import time:       163 |        163 |                         pandas._libs.properties
import time:      1044 |      16898 |                       pandas._libs.tslibs.offsets
import time:       773 |        773 |                           _decimal
import time:       237 |       1009 |                         decimal
import time:        33 |         33 |                               _string
import time:       831 |        864 |                             string
import time:       245 |        245 |                             dateutil._common
import time:      1582 |       2690 |                           dateutil.parser._parser
import time:       394 |        394 |                           dateutil.parser.isoparser
import time:       434 |       3517 |                         dateutil.parser
import time:       798 |        798 |                         pandas._libs.tslibs.strptime
import time:       697 |       6019 |                       pandas._libs.tslibs.parsing
import time:       567 |      23628 |                     pandas._libs.tslibs.conversion
import time:       558 |        558 |                     pandas._libs.tslibs.period
import time:       442 |        442 |                     pandas._libs.tslibs.vectorized
import time:       521 |      26378 |                   pandas._libs.tslibs
import time:        48 |      26425 |                 pandas._libs.tslibs.nattype
import time:       195 |        195 |                 pandas._libs.ops_dispatch
import time:       595 |      27214 |               pandas._libs.missing
import time:       910 |      28123 |             pandas._libs.hashtable
import time:      3591 |       3591 |             pandas._libs.algos
import time:       790 |      32503 |           pandas._libs.interval
import time:       291 |      33211 |         pandas._libs
import time:       144 |        144 |           pandas.core.dtypes
import time:        62 |         62 |             pyarrow
import time:       948 |       1009 |           pandas._libs.lib
import time:       483 |        483 |           pandas.errors
import time:       469 |        469 |             pandas.core.dtypes.generic
import time:       459 |        928 |           pandas.core.dtypes.base
import time:       224 |        224 |           pandas.core.dtypes.inference
import time:      1613 |       4399 |         pandas.core.dtypes.dtypes
import time:       260 |        260 |           pandas.core.dtypes.common
import time:       402 |        661 |         pandas.core.dtypes.missing
import time:       170 |        170 |           pandas.util._decorators
import time:        78 |         78 |               pandas.io
import time:       285 |        363 |             pandas.io._util
import time:       517 |        879 |           pandas.core.dtypes.cast
import time:       107 |        107 |             pandas.core.dtypes.astype
import time:       174 |        281 |           pandas.core.dtypes.concat
import time:        70 |         70 |             pandas.core.array_algos
import time:      3181 |       3181 |                 numpy.ma.core
import time:       832 |        832 |                 numpy.ma.extras
import time:       467 |       4479 |               numpy.ma
import time:       252 |        252 |               pandas.core.common
import time:       320 |       5049 |             pandas.core.construction
import time:       304 |       5422 |           pandas.core.array_algos.take
import time:       159 |        159 |             pandas.core.indexers.utils
import time:       163 |        322 |           pandas.core.indexers
import time:       675 |       7746 |         pandas.core.algorithms
import time:       205 |        205 |             pandas.core.arrays.arrow.accessors
import time:       186 |        186 |               unicodedata
import time:       180 |        180 |               pandas.util._validators
import time:       277 |        277 |               pandas.core.missing
import time:       306 |        306 |                   pandas._libs.ops
import time:        87 |         87 |                   pandas.core.roperator
import time:        68 |         68 |                   pandas.core.computation
import time:       127 |        127 |                     pandas.core.computation.check
import time:       243 |        370 |                   pandas.core.computation.expressions
import time:        92 |         92 |                   pandas.core.ops.missing
import time:        75 |         75 |                   pandas.core.ops.dispatch
import time:        71 |         71 |                   pandas.core.ops.invalid
import time:       544 |       1610 |                 pandas.core.ops.array_ops
import time:        98 |         98 |                 pandas.core.ops.common
import time:       117 |        117 |                 pandas.core.ops.docstrings
import time:        82 |         82 |                 pandas.core.ops.mask_ops
import time:       268 |       2172 |               pandas.core.ops
import time:       272 |        272 |               pandas.core.arraylike
import time:       217 |        217 |               pandas.core.arrays._arrow_string_mixins
import time:        93 |         93 |               pandas.core.arrays._utils
import time:       257 |        257 |                 pandas.compat.numpy.function
import time:       116 |        116 |                 pandas.core.array_algos.quantile
import time:       192 |        192 |                 pandas.core.sorting
import time:       928 |       1491 |               pandas.core.arrays.base
import time:       660 |        660 |                 pandas.core.nanops
import time:       117 |        117 |                 pandas.core.array_algos.masked_accumulations
import time:        96 |         96 |                 pandas.core.array_algos.masked_reductions
import time:        69 |         69 |                   pandas.core.util
import time:       295 |        295 |                   pandas._libs.hashing
import time:       311 |        674 |                 pandas.core.util.hashing
import time:       842 |       2388 |               pandas.core.arrays.masked
import time:       214 |        214 |                 pandas._libs.arrays
import time:       180 |        180 |                   pandas.core.arrays.numeric
import time:       195 |        374 |                 pandas.core.arrays.floating
import time:       195 |        195 |                 pandas.core.arrays.integer
import time:        81 |         81 |                     pandas.core.array_algos.transforms
import time:       624 |        705 |                   pandas.core.arrays._mixins
import time:        84 |         84 |                     pandas.core.strings
import time:       186 |        186 |                     pandas.core.strings.base
import time:       518 |        786 |                   pandas.core.strings.object_array
import time:       468 |       1958 |                 pandas.core.arrays.numpy_
import time:        90 |         90 |                 pandas.io.formats
import time:       138 |        138 |                       fnmatch
import time:       222 |        222 |                       zlib
import time:       719 |       1078 |                     shutil
import time:       119 |       1197 |                   pandas.io.formats.console
import time:       320 |       1517 |                 pandas.io.formats.printing
import time:       667 |       5012 |               pandas.core.arrays.string_
import time:        97 |         97 |                 pandas.tseries
import time:       331 |        427 |               pandas.tseries.frequencies
import time:      1647 |      14356 |             pandas.core.arrays.arrow.array
import time:       222 |      14781 |           pandas.core.arrays.arrow
import time:       210 |        210 |           pandas.core.arrays.boolean
import time:       160 |        160 |               _csv
import time:       341 |        500 |             csv
import time:       239 |        239 |             pandas.core.accessor
import time:       467 |        467 |             pandas.core.base
import time:      1011 |       2215 |           pandas.core.arrays.categorical
import time:       419 |        419 |             pandas._libs.tslib
import time:       102 |        102 |               pandas.core.array_algos.datetimelike_accumulations
import time:      1073 |       1174 |             pandas.core.arrays.datetimelike
import time:       307 |        307 |             pandas.core.arrays._ranges
import time:       119 |        119 |             pandas.tseries.offsets
import time:       704 |       2721 |           pandas.core.arrays.datetimes
import time:       397 |        397 |             pandas.core.arrays.timedeltas
import time:      1057 |       1453 |           pandas.core.arrays.interval
import time:       423 |        423 |           pandas.core.arrays.period
import time:       390 |        390 |                 pandas._libs.sparse
import time:       814 |       1203 |               pandas.core.arrays.sparse.array
import time:       325 |       1527 |             pandas.core.arrays.sparse.accessor
import time:       134 |       1661 |           pandas.core.arrays.sparse
import time:       369 |        369 |           pandas.core.arrays.string_arrow
import time:       568 |      24396 |         pandas.core.arrays
import time:       149 |        149 |         pandas.core.flags
import time:       348 |        348 |               pandas._libs.internals
import time:        91 |         91 |                 pandas.core._numba
import time:       291 |        381 |               pandas.core._numba.executor
import time:       732 |       1459 |             pandas.core.apply
import time:        53 |         53 |                 gc
import time:       144 |        144 |                       _json
import time:       371 |        514 |                     json.scanner
import time:       404 |        918 |                   json.decoder
import time:       322 |        322 |                   json.encoder
import time:       235 |       1474 |                 json
import time:       204 |        204 |                   pandas._libs.indexing
import time:        93 |         93 |                     pandas.core.indexes
import time:       678 |        678 |                       pandas._libs.index
import time:       323 |        323 |                       pandas._libs.writers
import time:       390 |        390 |                       pandas._libs.join
import time:       116 |        116 |                       pandas.core.array_algos.putmask
import time:       125 |        125 |                       pandas.core.indexes.frozen
import time:      1683 |       1683 |                       pandas.core.strings.accessor
import time:      2409 |       5721 |                     pandas.core.indexes.base
import time:       210 |        210 |                       pandas.core.indexes.extension
import time:       337 |        546 |                     pandas.core.indexes.category
import time:       423 |        423 |                         pandas.core.indexes.range
import time:        83 |         83 |                           pandas.core.tools
import time:       290 |        372 |                         pandas.core.tools.timedeltas
import time:       627 |       1422 |                       pandas.core.indexes.datetimelike
import time:       107 |        107 |                       pandas.core.tools.times
import time:       722 |       2250 |                     pandas.core.indexes.datetimes
import time:       990 |        990 |                       pandas.core.indexes.multi
import time:       219 |        219 |                       pandas.core.indexes.timedeltas
import time:      3044 |       4252 |                     pandas.core.indexes.interval
import time:       366 |        366 |                     pandas.core.indexes.period
import time:       559 |      13784 |                   pandas.core.indexes.api
import time:       982 |      14969 |                 pandas.core.indexing
import time:       133 |        133 |                 pandas.core.sample
import time:       140 |        140 |                 pandas.core.array_algos.replace
import time:       972 |        972 |                     pandas.core.internals.blocks
import time:       257 |       1229 |                   pandas.core.internals.api
import time:       225 |        225 |                     pandas.core.internals.base
import time:       302 |        302 |                       pandas.core.internals.ops
import time:       642 |        943 |                     pandas.core.internals.managers
import time:       558 |       1726 |                   pandas.core.internals.array_manager
import time:       242 |        242 |                   pandas.core.internals.concat
import time:       224 |       3419 |                 pandas.core.internals
import time:       225 |        225 |                 pandas.core.internals.construction
import time:        77 |         77 |                   pandas.core.methods
import time:        60 |         60 |                     pandas.core.reshape
import time:       474 |        533 |                   pandas.core.reshape.concat
import time:       326 |        326 |                       gzip
import time:       242 |        242 |                       mmap
import time:       319 |        319 |                           glob
import time:       483 |        802 |                         pathlib._abc
import time:        62 |         62 |                             _winapi
import time:        50 |         50 |                             nt
import time:        34 |         34 |                             nt
import time:        33 |         33 |                             nt
import time:        56 |         56 |                             nt
import time:        29 |         29 |                             nt
import time:        29 |         29 |                             nt
import time:        27 |         27 |                             nt
import time:       373 |        689 |                           ntpath
import time:        46 |         46 |                           pwd
import time:       193 |        193 |                           grp
import time:       616 |       1542 |                         pathlib._local
import time:       190 |       2533 |                       pathlib
import time:       938 |        938 |                       tarfile
import time:        85 |         85 |                         urllib
import time:       996 |        996 |                         ipaddress
import time:      1020 |       2099 |                       urllib.parse
import time:       112 |        112 |                           zipfile._path.glob
import time:       335 |        446 |                         zipfile._path
import time:       697 |       1143 |                       zipfile
import time:        93 |         93 |                       pandas.core.shared_docs
import time:      1603 |       8973 |                     pandas.io.common
import time:       718 |       9691 |                   pandas.io.formats.format
import time:       344 |      10643 |                 pandas.core.methods.describe
import time:        66 |         66 |                       pandas._libs.window
import time:       524 |        590 |                     pandas._libs.window.aggregations
import time:       232 |        232 |                       pandas._libs.window.indexers
import time:       311 |        542 |                     pandas.core.indexers.objects
import time:       131 |        131 |                     pandas.core.util.numba_
import time:       110 |        110 |                     pandas.core.window.common
import time:       164 |        164 |                     pandas.core.window.doc
import time:       341 |        341 |                     pandas.core.window.numba_
import time:       108 |        108 |                     pandas.core.window.online
import time:      1290 |       1290 |                     pandas.core.window.rolling
import time:       651 |       3923 |                   pandas.core.window.ewm
import time:       614 |        614 |                   pandas.core.window.expanding
import time:       145 |       4680 |                 pandas.core.window
import time:      3190 |      38923 |               pandas.core.generic
import time:       266 |        266 |               pandas.core.methods.selectn
import time:        77 |         77 |                 pandas.core.reshape.util
import time:       108 |        108 |                 pandas.core.tools.numeric
import time:       246 |        430 |               pandas.core.reshape.melt
import time:       281 |        281 |                 pandas._libs.reshape
import time:       507 |        507 |                 pandas.core.indexes.accessors
import time:        88 |         88 |                   pandas.arrays
import time:       520 |        608 |                 pandas.core.tools.datetimes
import time:       645 |        645 |                 pandas.io.formats.info
import time:       576 |        576 |                   pandas.plotting._core
import time:       153 |        153 |                   pandas.plotting._misc
import time:       181 |        909 |                 pandas.plotting
import time:      2557 |       5504 |               pandas.core.series
import time:      4503 |      49624 |             pandas.core.frame
import time:       816 |        816 |             pandas.core.groupby.base
import time:       617 |        617 |               pandas._libs.groupby
import time:        90 |         90 |                 pandas.core.groupby.categorical
import time:       354 |        443 |               pandas.core.groupby.grouper
import time:       701 |       1760 |             pandas.core.groupby.ops
import time:       116 |        116 |               pandas.core.groupby.numba_
import time:       174 |        174 |               pandas.core.groupby.indexing
import time:      1301 |       1590 |             pandas.core.groupby.groupby
import time:      1331 |      56578 |           pandas.core.groupby.generic
import time:       110 |      56687 |         pandas.core.groupby
import time:       513 |     127761 |       pandas.core.api
import time:        95 |         95 |       pandas.tseries.api
import time:        57 |         57 |               pandas.core.computation.common
import time:       130 |        187 |             pandas.core.computation.align
import time:       193 |        193 |                 pprint
import time:       198 |        390 |               pandas.core.computation.scope
import time:       284 |        674 |             pandas.core.computation.ops
import time:       134 |        994 |           pandas.core.computation.engines
import time:       266 |        266 |             pandas.core.computation.parsing
import time:       704 |        970 |           pandas.core.computation.expr
import time:       145 |       2108 |         pandas.core.computation.eval
import time:        89 |       2196 |       pandas.core.computation.api
import time:       134 |        134 |         pandas.core.reshape.encoding
import time:       200 |        200 |             _uuid
import time:       352 |        551 |           uuid
import time:       564 |       1115 |         pandas.core.reshape.merge
import time:       438 |        438 |         pandas.core.reshape.pivot
import time:       168 |        168 |         pandas.core.reshape.tile
import time:       208 |       2061 |       pandas.core.reshape.api
import time:        99 |         99 |         pandas.api.extensions
import time:        55 |         55 |         pandas.api.indexers
import time:        45 |         45 |             pandas.core.interchange
import time:       677 |        722 |           pandas.core.interchange.dataframe_protocol
import time:       315 |        315 |             pandas.core.interchange.utils
import time:       210 |        525 |           pandas.core.interchange.from_dataframe
import time:        78 |       1324 |         pandas.api.interchange
import time:        72 |         72 |           pandas.core.dtypes.api
import time:       145 |        217 |         pandas.api.types
import time:       782 |        782 |           pandas.core.resample
import time:       164 |        164 |                 pandas._libs.json
import time:       125 |        125 |                 pandas.io.json._normalize
import time:       115 |        115 |                 pandas.io.json._table_schema
import time:       397 |        397 |                       pandas._libs.parsers
import time:       390 |        390 |                         pandas.io.parsers.base_parser
import time:       190 |        580 |                       pandas.io.parsers.arrow_parser_wrapper
import time:       161 |        161 |                       pandas.io.parsers.c_parser_wrapper
import time:       287 |        287 |                       pandas.io.parsers.python_parser
import time:      1450 |       2874 |                     pandas.io.parsers.readers
import time:       110 |       2983 |                   pandas.io.parsers
import time:        19 |       3002 |                 pandas.io.parsers.readers
import time:       612 |       4016 |               pandas.io.json._json
import time:       139 |       4155 |             pandas.io.json
import time:        30 |       4184 |           pandas.io.json._json
import time:      1038 |       1038 |           pandas.io.stata
import time:       120 |       6123 |         pandas.api.typing
import time:       161 |       7975 |       pandas.api
import time:       268 |        268 |               tempfile
import time:       157 |        424 |             pandas._testing.contexts
import time:       126 |        550 |           pandas._testing._io
import time:       109 |        109 |           pandas._testing._warnings
import time:       138 |        138 |               cmath
import time:       202 |        340 |             pandas._libs.testing
import time:       261 |        600 |           pandas._testing.asserters
import time:        84 |         84 |           pandas._testing.compat
import time:       347 |       1689 |         pandas._testing
import time:       116 |       1804 |       pandas.testing
import time:       109 |        109 |       pandas.util._print_versions
import time:        79 |         79 |         pandas.io.clipboards
import time:      2131 |       2131 |             pandas.io.excel._util
import time:       237 |        237 |             pandas.io.excel._calamine
import time:       191 |        191 |             pandas.io.excel._odfreader
import time:       273 |        273 |             pandas.io.excel._openpyxl
import time:       142 |        142 |             pandas.io.excel._pyxlsb
import time:       155 |        155 |             pandas.io.excel._xlrd
import time:       946 |       4073 |           pandas.io.excel._base
import time:       149 |        149 |           pandas.io.excel._odswriter
import time:       152 |        152 |           pandas.io.excel._xlsxwriter
import time:       101 |       4473 |         pandas.io.excel
import time:       118 |        118 |         pandas.io.feather_format
import time:       110 |        110 |         pandas.io.gbq
import time:       436 |        436 |         pandas.io.html
import time:        96 |         96 |         pandas.io.orc
import time:       267 |        267 |         pandas.io.parquet
import time:       140 |        140 |           pandas.compat.pickle_compat
import time:       182 |        322 |         pandas.io.pickle
import time:       402 |        402 |           pandas.core.computation.pytables
import time:      1354 |       1755 |         pandas.io.pytables
import time:       181 |        181 |           pandas.io.sas.sasreader
import time:       127 |        308 |         pandas.io.sas
import time:        82 |         82 |         pandas.io.spss
import time:       509 |        509 |         pandas.io.sql
import time:       396 |        396 |         pandas.io.xml
import time:       219 |       9164 |       pandas.io.api
import time:        82 |         82 |       pandas.util._tester
import time:        53 |         53 |       pandas._version_meson
import time:       726 |     188890 |     pandas
import time:       280 |        280 |       numpy._typing._add_docstring
import time:       110 |        390 |     numpy.typing
import time:       128 |        128 |         _colorize
import time:       659 |        786 |       traceback
import time:       225 |       1010 |     optimagic.exceptions
import time:        80 |         80 |       optimagic.optimization
import time:       144 |        223 |     optimagic.optimization.algo_options
import time:       320 |        320 |           _socket
import time:       869 |       1189 |         typing_extensions
import time:      4538 |       5726 |       annotated_types
import time:      1931 |       7657 |     optimagic.typing
import time:      3817 |     271863 |   optimagic.constraints
import time:       171 |        171 |                 sqlalchemy.util.preloaded
import time:        56 |         56 |                     sqlalchemy.cyextension
import time:       300 |        300 |                     sqlalchemy.cyextension.collections
import time:       189 |        189 |                     sqlalchemy.cyextension.immutabledict
import time:       133 |        133 |                     sqlalchemy.cyextension.processors
import time:       128 |        128 |                     sqlalchemy.cyextension.resultproxy
import time:       131 |        131 |                             email
import time:       239 |        239 |                             importlib.metadata._meta
import time:       174 |        174 |                             importlib.metadata._collections
import time:        69 |         69 |                             importlib.metadata._functools
import time:        60 |         60 |                             importlib.metadata._itertools
import time:       271 |        271 |                                   importlib.resources.abc
import time:       361 |        632 |                                 importlib.resources._common
import time:       116 |        116 |                                 importlib.resources._functional
import time:       180 |        927 |                               importlib.resources
import time:       265 |       1191 |                             importlib.abc
import time:       969 |       2831 |                           importlib.metadata
import time:       404 |       3234 |                         sqlalchemy.util.compat
import time:       821 |       4055 |                       sqlalchemy.exc
import time:       409 |       4464 |                     sqlalchemy.cyextension.util
import time:       206 |       5474 |                   sqlalchemy.util._has_cy
import time:       580 |        580 |                   sqlalchemy.util.typing
import time:       643 |       6696 |                 sqlalchemy.util._collections
import time:        69 |         69 |                         concurrent
import time:        42 |         42 |                             atexit
import time:      1174 |       1215 |                           logging
import time:       539 |       1754 |                         concurrent.futures._base
import time:       176 |       1997 |                       concurrent.futures
import time:       129 |        129 |                         _heapq
import time:       210 |        338 |                       heapq
import time:       187 |        187 |                         array
import time:       923 |       1110 |                       socket
import time:       935 |        935 |                         _ssl
import time:      1399 |       2333 |                       ssl
import time:       229 |        229 |                       asyncio.constants
import time:        93 |         93 |                       asyncio.coroutines
import time:        92 |         92 |                         asyncio.format_helpers
import time:        87 |         87 |                           asyncio.base_futures
import time:       125 |        125 |                           asyncio.exceptions
import time:        95 |         95 |                           asyncio.base_tasks
import time:       271 |        577 |                         _asyncio
import time:       382 |       1050 |                       asyncio.events
import time:       163 |        163 |                       asyncio.futures
import time:       119 |        119 |                       asyncio.protocols
import time:       167 |        167 |                         asyncio.transports
import time:        88 |         88 |                         asyncio.log
import time:       608 |        863 |                       asyncio.sslproto
import time:        72 |         72 |                           asyncio.mixins
import time:       326 |        398 |                         asyncio.locks
import time:       175 |        175 |                           asyncio.queues
import time:       283 |        283 |                           asyncio.timeouts
import time:       448 |        905 |                         asyncio.tasks
import time:       176 |       1478 |                       asyncio.staggered
import time:       140 |        140 |                       asyncio.trsock
import time:       784 |      10691 |                     asyncio.base_events
import time:       218 |        218 |                     asyncio.runners
import time:       270 |        270 |                     asyncio.streams
import time:       157 |        157 |                     asyncio.subprocess
import time:       105 |        105 |                     asyncio.taskgroups
import time:        88 |         88 |                     asyncio.threads
import time:       173 |        173 |                       asyncio.base_subprocess
import time:       566 |        566 |                       asyncio.selector_events
import time:      6774 |       7511 |                     asyncio.unix_events
import time:       199 |      19236 |                   asyncio
import time:       267 |        267 |                     greenlet._greenlet
import time:       148 |        414 |                   greenlet
import time:      1249 |       1249 |                     sqlalchemy.util.langhelpers
import time:       322 |       1570 |                   sqlalchemy.util._concurrency_py3k
import time:       147 |      21366 |                 sqlalchemy.util.concurrency
import time:       220 |        220 |                 sqlalchemy.util.deprecations
import time:       328 |      28779 |               sqlalchemy.util
import time:       571 |        571 |                                 sqlalchemy.event.registry
import time:       206 |        776 |                               sqlalchemy.event.legacy
import time:       638 |       1413 |                             sqlalchemy.event.attr
import time:       390 |       1802 |                           sqlalchemy.event.base
import time:       159 |       1960 |                         sqlalchemy.event.api
import time:       101 |       2060 |                       sqlalchemy.event
import time:       266 |        266 |                             sqlalchemy.log
import time:      1530 |       1795 |                           sqlalchemy.pool.base
import time:       777 |       2572 |                         sqlalchemy.pool.events
import time:       251 |        251 |                           sqlalchemy.util.queue
import time:       351 |        601 |                         sqlalchemy.pool.impl
import time:       144 |       3316 |                       sqlalchemy.pool
import time:       515 |        515 |                             sqlalchemy.sql.roles
import time:       253 |        253 |                             sqlalchemy.inspection
import time:      1135 |       1902 |                           sqlalchemy.sql._typing
import time:      2876 |       2876 |                             sqlalchemy.sql.visitors
import time:       754 |        754 |                             sqlalchemy.sql.cache_key
import time:       588 |        588 |                               sqlalchemy.sql.operators
import time:       526 |       1113 |                             sqlalchemy.sql.traversals
import time:      1894 |       6636 |                           sqlalchemy.sql.base
import time:       967 |        967 |                             sqlalchemy.sql.coercions
import time:       315 |        315 |                                   sqlalchemy.sql.annotation
import time:      1182 |       1182 |                                       sqlalchemy.sql.type_api
import time:      4554 |       5736 |                                     sqlalchemy.sql.elements
import time:       134 |        134 |                                     sqlalchemy.util.topological
import time:      1331 |       7200 |                                   sqlalchemy.sql.ddl
import time:       121 |        121 |                                           sqlalchemy.engine._py_processors
import time:       122 |        243 |                                         sqlalchemy.engine.processors
import time:      2144 |       2387 |                                       sqlalchemy.sql.sqltypes
import time:      5988 |       8374 |                                     sqlalchemy.sql.selectable
import time:      3064 |      11438 |                                   sqlalchemy.sql.schema
import time:       706 |      19657 |                                 sqlalchemy.sql.util
import time:      2389 |      22046 |                               sqlalchemy.sql.dml
import time:       647 |      22692 |                             sqlalchemy.sql.crud
import time:      2420 |       2420 |                             sqlalchemy.sql.functions
import time:      4646 |      30724 |                           sqlalchemy.sql.compiler
import time:       129 |        129 |                             sqlalchemy.sql._dml_constructors
import time:       251 |        251 |                             sqlalchemy.sql._elements_constructors
import time:       224 |        224 |                             sqlalchemy.sql._selectable_constructors
import time:       663 |        663 |                             sqlalchemy.sql.lambdas
import time:       383 |       1648 |                           sqlalchemy.sql.expression
import time:       233 |        233 |                           sqlalchemy.sql.default_comparator
import time:       468 |        468 |                             sqlalchemy.sql.events
import time:       372 |        840 |                           sqlalchemy.sql.naming
import time:      4246 |      46226 |                         sqlalchemy.sql
import time:        16 |      46242 |                       sqlalchemy.sql.compiler
import time:      2091 |      53707 |                     sqlalchemy.engine.interfaces
import time:       208 |        208 |                     sqlalchemy.engine.util
import time:      1824 |      55738 |                   sqlalchemy.engine.base
import time:      1189 |      56926 |                 sqlalchemy.engine.events
import time:        86 |         86 |                     sqlalchemy.dialects
import time:       540 |        626 |                   sqlalchemy.engine.url
import time:       225 |        225 |                   sqlalchemy.engine.mock
import time:       527 |       1377 |                 sqlalchemy.engine.create
import time:       727 |        727 |                     sqlalchemy.engine.row
import time:      1362 |       2089 |                   sqlalchemy.engine.result
import time:       725 |       2813 |                 sqlalchemy.engine.cursor
import time:      1305 |       1305 |                 sqlalchemy.engine.reflection
import time:       274 |      62693 |               sqlalchemy.engine
import time:       153 |        153 |               sqlalchemy.schema
import time:       168 |        168 |               sqlalchemy.types
import time:       152 |        152 |                 sqlalchemy.engine.characteristics
import time:      1006 |       1157 |               sqlalchemy.engine.default
import time:       478 |      93425 |             sqlalchemy
import time:       590 |        590 |                 cloudpickle.cloudpickle
import time:       146 |        735 |               cloudpickle
import time:       301 |       1036 |             optimagic.logging.base
import time:        47 |         47 |                           jax
import time:        68 |        114 |                         pybaum.config
import time:       119 |        232 |                       pybaum.registry_entries
import time:        89 |        321 |                     pybaum.registry
import time:        56 |         56 |                       pybaum.equality
import time:        60 |         60 |                       pybaum.typecheck
import time:       146 |        261 |                     pybaum.tree_util
import time:       104 |        685 |                   pybaum
import time:        56 |         56 |                     optimagic.parameters
import time:       157 |        213 |                   optimagic.parameters.tree_registry
import time:       422 |        422 |                     difflib
import time:       222 |        222 |                         scipy.__config__
import time:        75 |         75 |                         scipy.version
import time:        23 |         23 |                           scipy._distributor_init_local
import time:        98 |        121 |                         scipy._distributor_init
import time:        41 |         41 |                             cython
import time:       241 |        282 |                           scipy._lib._testutils
import time:        82 |        363 |                         scipy._lib
import time:       266 |        266 |                         scipy._lib._pep440
import time:       319 |        319 |                           scipy._lib._ccallback_c
import time:       176 |        494 |                         scipy._lib._ccallback
import time:       269 |       1807 |                       scipy
import time:      1536 |       1536 |                           scipy.linalg._fblas
import time:        29 |         29 |                           scipy.linalg._cblas
import time:        17 |         17 |                           scipy.linalg._fblas_64
import time:       262 |       1842 |                         scipy.linalg.blas
import time:      1107 |       1107 |                           scipy.linalg._flapack
import time:        29 |         29 |                           scipy.linalg._clapack
import time:        16 |         16 |                           scipy.linalg._flapack_64
import time:       564 |       1715 |                         scipy.linalg.lapack
import time:       327 |       3883 |                       scipy.linalg._misc
import time:       293 |        293 |                         scipy._cyutility
import time:       924 |        924 |                         scipy.linalg.cython_lapack
import time:      1409 |       1409 |                                   scipy._lib.array_api_compat.common._typing
import time:      3104 |       4513 |                                 scipy._lib.array_api_compat.common._helpers
import time:       110 |       4622 |                               scipy._lib.array_api_compat.common
import time:       485 |       5107 |                             scipy._lib.array_api_compat
import time:       470 |        470 |                                 numpy.fft._helper
import time:       304 |        304 |                                   numpy.fft._pocketfft_umath
import time:       816 |       1119 |                                 numpy.fft._pocketfft
import time:       287 |        287 |                                 numpy.fft.helper
import time:      1613 |       3488 |                               numpy.fft
import time:       290 |        290 |                                     unittest.util
import time:       446 |        735 |                                   unittest.result
import time:       967 |        967 |                                   unittest.case
import time:       335 |        335 |                                   unittest.suite
import time:       734 |        734 |                                   unittest.loader
import time:      1411 |       1411 |                                       gettext
import time:      1431 |       2841 |                                     argparse
import time:       188 |        188 |                                       unittest.signals
import time:       301 |        489 |                                     unittest.runner
import time:       349 |       3678 |                                   unittest.main
import time:      1268 |       7716 |                                 unittest
import time:       339 |        339 |                                 numpy.testing._private
import time:       315 |        315 |                                 numpy.testing.overrides
import time:       380 |        380 |                                 numpy.testing._private.extbuild
import time:      3349 |       3349 |                                 numpy.testing._private.utils
import time:       355 |      12452 |                               numpy.testing
import time:       343 |        343 |                                 numpy.f2py.diagnose
import time:       174 |        174 |                                   numpy.f2py._backends
import time:        94 |         94 |                                   numpy.f2py.__version__
import time:       270 |        270 |                                     numpy.f2py.cfuncs
import time:       987 |       1257 |                                   numpy.f2py.auxfuncs
import time:      1976 |       1976 |                                     numpy.f2py.cb_rules
import time:       188 |        188 |                                     numpy.f2py._isocbind
import time:       302 |        302 |                                       fileinput
import time:      1172 |       1172 |                                             charset_normalizer.constant
import time:       290 |        290 |                                                 _multibytecodec
import time:       449 |        739 |                                               charset_normalizer.utils
import time:       447 |       1185 |                                             charset_normalizer.md
import time:       754 |        754 |                                             charset_normalizer.models
import time:       383 |       3494 |                                           charset_normalizer.cd
import time:       378 |       3871 |                                         charset_normalizer.api
import time:       194 |        194 |                                         charset_normalizer.legacy
import time:       119 |        119 |                                         charset_normalizer.version
import time:       244 |       4427 |                                       charset_normalizer
import time:      1339 |       1339 |                                       numpy.f2py.symbolic
import time:     12147 |      18213 |                                     numpy.f2py.crackfortran
import time:       637 |      21012 |                                   numpy.f2py.capi_maps
import time:       176 |        176 |                                     numpy.f2py.func2subr
import time:       326 |        502 |                                   numpy.f2py.f90mod_rules
import time:       111 |        111 |                                     numpy.f2py.common_rules
import time:        81 |         81 |                                     numpy.f2py.use_rules
import time:      4370 |       4561 |                                   numpy.f2py.rules
import time:       861 |      28457 |                                 numpy.f2py.f2py2e
import time:       323 |      29122 |                               numpy.f2py
import time:       254 |        254 |                                   numpy.polynomial.polyutils
import time:       522 |        522 |                                   numpy.polynomial._polybase
import time:      1156 |       1931 |                                 numpy.polynomial.chebyshev
import time:       971 |        971 |                                 numpy.polynomial.hermite
import time:       881 |        881 |                                 numpy.polynomial.hermite_e
import time:       893 |        893 |                                 numpy.polynomial.laguerre
import time:       867 |        867 |                                 numpy.polynomial.legendre
import time:       871 |        871 |                                 numpy.polynomial.polynomial
import time:       534 |       6944 |                               numpy.polynomial
import time:       548 |        548 |                                 numpy.core._utils
import time:       259 |        807 |                               numpy.core
import time:       216 |        216 |                               numpy.rec
import time:      3985 |       3985 |                                 numpy._core.strings
import time:       224 |       4208 |                               numpy.strings
import time:      1979 |       1979 |                                 numpy._core.defchararray
import time:       218 |       2197 |                               numpy.char
import time:      1481 |       1481 |                                 numpy.ctypeslib._ctypeslib
import time:       222 |       1702 |                               numpy.ctypeslib
import time:       290 |        290 |                                 scipy._lib.array_api_compat._internal
import time:      2140 |       2140 |                                 scipy._lib.array_api_compat.common._aliases
import time:       476 |        476 |                                   scipy._lib.array_api_compat.numpy._typing
import time:       401 |        876 |                                 scipy._lib.array_api_compat.numpy._info
import time:      6252 |       9556 |                               scipy._lib.array_api_compat.numpy._aliases
import time:       896 |        896 |                                 scipy._lib.array_api_compat.common._linalg
import time:      1297 |       2192 |                               scipy._lib.array_api_compat.numpy.linalg
import time:       317 |        317 |                                 scipy._lib.array_api_compat.common._fft
import time:       839 |       1155 |                               scipy._lib.array_api_compat.numpy.fft
import time:       611 |      74643 |                             scipy._lib.array_api_compat.numpy
import time:       129 |        129 |                             scipy._lib._sparse
import time:       254 |        254 |                                 pkgutil
import time:       159 |        159 |                                   _pyrepl
import time:       156 |        314 |                                 _pyrepl.pager
import time:      1273 |       1841 |                               pydoc
import time:      1168 |       3009 |                             scipy._lib._docscrape
import time:      8087 |      90973 |                           scipy._lib._array_api
import time:       705 |      91678 |                         scipy._lib._util
import time:      1158 |      94051 |                       scipy.linalg._cythonized_array_utils
import time:      2830 |       2830 |                         scipy.linalg._decomp
import time:      1100 |       1100 |                         scipy.linalg._decomp_svd
import time:       233 |        233 |                         scipy.linalg._solve_toeplitz
import time:      2246 |       6408 |                       scipy.linalg._basic
import time:       179 |        179 |                         scipy.linalg._decomp_lu_cython
import time:       478 |        657 |                       scipy.linalg._decomp_lu
import time:       496 |        496 |                       scipy.linalg._decomp_ldl
import time:       703 |        703 |                       scipy.linalg._decomp_cholesky
import time:       800 |        800 |                       scipy.linalg._decomp_qr
import time:      1086 |       1086 |                       scipy.linalg._decomp_qz
import time:       658 |        658 |                       scipy.linalg._decomp_schur
import time:       745 |        745 |                       scipy.linalg._decomp_polar
import time:       146 |        146 |                         scipy._lib.deprecation
import time:       534 |        534 |                         scipy.linalg._expm_frechet
import time:       528 |        528 |                         scipy.linalg._matfuncs_schur_sqrtm
import time:       371 |        371 |                         scipy.linalg._matfuncs_expm
import time:       223 |        223 |                         scipy.linalg._linalg_pythran
import time:      1781 |       3581 |                       scipy.linalg._matfuncs
import time:       460 |        460 |                       scipy.linalg._special_matrices
import time:      1283 |       1283 |                       scipy.linalg._solvers
import time:       329 |        329 |                       scipy.linalg._procrustes
import time:       394 |        394 |                         scipy.linalg.cython_blas
import time:      1264 |       1657 |                       scipy.linalg._decomp_update
import time:       315 |        315 |                             scipy.sparse._sputils
import time:       171 |        171 |                             scipy.sparse._matrix
import time:       553 |       1037 |                           scipy.sparse._base
import time:       237 |        237 |                             scipy.sparse._sparsetools
import time:       226 |        226 |                               scipy.sparse._data
import time:       198 |        198 |                               scipy.sparse._index
import time:       590 |       1012 |                             scipy.sparse._compressed
import time:       252 |       1500 |                           scipy.sparse._csr
import time:       249 |        249 |                           scipy.sparse._csc
import time:       514 |        514 |                             scipy.sparse._csparsetools
import time:       291 |        804 |                           scipy.sparse._lil
import time:       341 |        341 |                           scipy.sparse._dok
import time:       439 |        439 |                           scipy.sparse._coo
import time:       230 |        230 |                           scipy.sparse._dia
import time:       264 |        264 |                           scipy.sparse._bsr
import time:      1024 |       1024 |                           scipy.sparse._construct
import time:        95 |         95 |                           scipy.sparse._extract
import time:        75 |         75 |                           scipy.sparse._matrix_io
import time:        54 |         54 |                           scipy.sparse.base
import time:        45 |         45 |                           scipy.sparse.bsr
import time:        89 |         89 |                           scipy.sparse.compressed
import time:        50 |         50 |                           scipy.sparse.construct
import time:        47 |         47 |                           scipy.sparse.coo
import time:        45 |         45 |                           scipy.sparse.csc
import time:        46 |         46 |                           scipy.sparse.csr
import time:        42 |         42 |                           scipy.sparse.data
import time:        42 |         42 |                           scipy.sparse.dia
import time:        83 |         83 |                           scipy.sparse.dok
import time:        41 |         41 |                           scipy.sparse.extract
import time:        39 |         39 |                           scipy.sparse.lil
import time:        42 |         42 |                           scipy.sparse.sparsetools
import time:        43 |         43 |                           scipy.sparse.sputils
import time:       627 |       7381 |                         scipy.sparse
import time:       474 |       7854 |                       scipy.linalg._sketches
import time:       210 |        210 |                       scipy.linalg._decomp_cossin
import time:        75 |         75 |                       scipy.linalg.decomp
import time:        56 |         56 |                       scipy.linalg.decomp_cholesky
import time:        57 |         57 |                       scipy.linalg.decomp_lu
import time:        49 |         49 |                       scipy.linalg.decomp_qr
import time:        57 |         57 |                       scipy.linalg.decomp_svd
import time:        49 |         49 |                       scipy.linalg.decomp_schur
import time:        49 |         49 |                       scipy.linalg.basic
import time:        75 |         75 |                       scipy.linalg.misc
import time:        54 |         54 |                       scipy.linalg.special_matrices
import time:        51 |         51 |                       scipy.linalg.matfuncs
import time:       651 |     127878 |                     scipy.linalg
import time:       171 |     128471 |                   optimagic.utilities
import time:      2012 |     131379 |                 optimagic.optimization.fun_value
import time:      2842 |     134220 |               optimagic.logging.types
import time:       725 |     134945 |             optimagic.logging.sqlalchemy
import time:       420 |     229824 |           optimagic.logging.logger
import time:        81 |     229904 |         optimagic.logging
import time:        27 |     229930 |       optimagic.logging.types
import time:       620 |        620 |         optimagic.timing
import time:       821 |       1441 |       optimagic.optimization.history
import time:       197 |        197 |                 _multiprocessing
import time:       270 |        270 |                     multiprocessing.process
import time:       279 |        279 |                     multiprocessing.reduction
import time:       606 |       1154 |                   multiprocessing.context
import time:       206 |       1360 |                 multiprocessing
import time:       218 |       1774 |               joblib._multiprocessing_helpers
import time:        80 |         80 |                 joblib.externals
import time:        90 |         90 |                 joblib.externals.loky._base
import time:       229 |        229 |                       multiprocessing.util
import time:       288 |        516 |                     multiprocessing.synchronize
import time:       180 |        180 |                           _queue
import time:       240 |        419 |                         queue
import time:        66 |         66 |                           _winapi
import time:       450 |        516 |                         multiprocessing.connection
import time:       218 |        218 |                         multiprocessing.queues
import time:       400 |       1551 |                       concurrent.futures.process
import time:       126 |        126 |                       joblib.externals.loky.backend.process
import time:       210 |       1886 |                     joblib.externals.loky.backend.context
import time:        82 |       2483 |                   joblib.externals.loky.backend
import time:        25 |       2508 |                 joblib.externals.loky.backend.context
import time:       106 |        106 |                   joblib.externals.loky.backend._posix_reduction
import time:       603 |        603 |                     joblib.externals.cloudpickle.cloudpickle
import time:       108 |        710 |                   joblib.externals.cloudpickle
import time:       181 |        996 |                 joblib.externals.loky.backend.reduction
import time:        48 |         48 |                     faulthandler
import time:       133 |        133 |                     joblib.externals.loky.backend.queues
import time:        50 |         50 |                       psutil
import time:       125 |        175 |                     joblib.externals.loky.backend.utils
import time:       108 |        108 |                     joblib.externals.loky.initializers
import time:        42 |         42 |                     psutil
import time:       499 |       1003 |                   joblib.externals.loky.process_executor
import time:       146 |       1149 |                 joblib.externals.loky.reusable_executor
import time:       102 |        102 |                 joblib.externals.loky.cloudpickle_wrapper
import time:       170 |       5093 |               joblib.externals.loky
import time:       109 |       6975 |             joblib._cloudpickle_wrapper
import time:       513 |        513 |               joblib._utils
import time:       364 |        364 |               multiprocessing.pool
import time:       249 |        249 |                   joblib.backports
import time:        89 |         89 |                   joblib.disk
import time:      2233 |       2233 |                         runpy
import time:       168 |       2400 |                       multiprocessing.spawn
import time:       322 |        322 |                       _posixshmem
import time:       158 |       2879 |                     multiprocessing.resource_tracker
import time:       123 |        123 |                     joblib.externals.loky.backend.spawn
import time:       148 |       3149 |                   joblib.externals.loky.backend.resource_tracker
import time:        51 |         51 |                       lz4
import time:       305 |        356 |                     joblib.compressor
import time:       106 |        106 |                       joblib.numpy_pickle_utils
import time:       162 |        267 |                     joblib.numpy_pickle_compat
import time:       242 |        864 |                   joblib.numpy_pickle
import time:       240 |       4590 |                 joblib._memmapping_reducer
import time:       129 |       4718 |               joblib.executor
import time:       169 |        169 |               joblib.pool
import time:       351 |       6113 |             joblib._parallel_backends
import time:       112 |        112 |               joblib.logger
import time:       405 |        517 |             joblib._store_backends
import time:       161 |        161 |             joblib.hashing
import time:       219 |        219 |               joblib.func_inspect
import time:       439 |        658 |             joblib.memory
import time:       383 |        383 |             joblib.parallel
import time:       196 |      14999 |           joblib
import time:        53 |         53 |             pathos
import time:        21 |         74 |           pathos.pools
import time:       287 |        287 |                         scipy.sparse.linalg._interface
import time:       114 |        114 |                         scipy.sparse.linalg._isolve.utils
import time:       182 |        582 |                       scipy.sparse.linalg._isolve.iterative
import time:        91 |         91 |                       scipy.sparse.linalg._isolve.minres
import time:        98 |         98 |                         scipy.sparse.linalg._isolve._gcrotmk
import time:       107 |        205 |                       scipy.sparse.linalg._isolve.lgmres
import time:        92 |         92 |                       scipy.sparse.linalg._isolve.lsqr
import time:       105 |        105 |                       scipy.sparse.linalg._isolve.lsmr
import time:        66 |         66 |                       scipy.sparse.linalg._isolve.tfqmr
import time:       168 |       1307 |                     scipy.sparse.linalg._isolve
import time:       426 |        426 |                         scipy.sparse.linalg._dsolve._superlu
import time:        49 |         49 |                           scikits
import time:        22 |         70 |                         scikits.umfpack
import time:       283 |        778 |                       scipy.sparse.linalg._dsolve.linsolve
import time:        86 |         86 |                       scipy.sparse.linalg._dsolve._add_newdocs
import time:       109 |        972 |                     scipy.sparse.linalg._dsolve
import time:       530 |        530 |                             scipy._lib.decorator
import time:       126 |        655 |                           scipy._lib._threadsafety
import time:       645 |        645 |                           scipy.sparse.linalg._eigen.arpack._arpack
import time:       383 |       1682 |                         scipy.sparse.linalg._eigen.arpack.arpack
import time:       100 |       1782 |                       scipy.sparse.linalg._eigen.arpack
import time:       211 |        211 |                         scipy.sparse.linalg._eigen.lobpcg.lobpcg
import time:       132 |        343 |                       scipy.sparse.linalg._eigen.lobpcg
import time:        48 |         48 |                           scipy.sparse.linalg._propack
import time:       460 |        460 |                           scipy.sparse.linalg._propack._spropack
import time:       372 |        372 |                           scipy.sparse.linalg._propack._dpropack
import time:       388 |        388 |                           scipy.sparse.linalg._propack._cpropack
import time:       371 |        371 |                           scipy.sparse.linalg._propack._zpropack
import time:       215 |       1851 |                         scipy.sparse.linalg._svdp
import time:       822 |       2673 |                       scipy.sparse.linalg._eigen._svds
import time:       142 |       4938 |                     scipy.sparse.linalg._eigen
import time:       108 |        108 |                         scipy.sparse.linalg._onenormest
import time:       187 |        295 |                       scipy.sparse.linalg._expm_multiply
import time:       278 |        572 |                     scipy.sparse.linalg._matfuncs
import time:        93 |         93 |                     scipy.sparse.linalg._norm
import time:       399 |        399 |                     scipy.sparse.linalg._special_sparse_arrays
import time:       104 |        104 |                     scipy.sparse.linalg.isolve
import time:        56 |         56 |                     scipy.sparse.linalg.dsolve
import time:        47 |         47 |                     scipy.sparse.linalg.interface
import time:        47 |         47 |                     scipy.sparse.linalg.eigen
import time:        47 |         47 |                     scipy.sparse.linalg.matfuncs
import time:       281 |       8857 |                   scipy.sparse.linalg
import time:       105 |        105 |                     scipy.optimize._dcsrch
import time:       173 |        278 |                   scipy.optimize._linesearch
import time:       161 |        161 |                     scipy.optimize._group_columns
import time:        53 |         53 |                         scipy._lib.array_api_extra._lib
import time:        57 |         57 |                             scipy._lib.array_api_extra._lib._utils
import time:       117 |        117 |                               scipy._lib._array_api_compat_vendor
import time:       158 |        275 |                             scipy._lib.array_api_extra._lib._utils._compat
import time:       126 |        126 |                               scipy._lib.array_api_extra._lib._utils._typing
import time:       233 |        359 |                             scipy._lib.array_api_extra._lib._utils._helpers
import time:       313 |       1002 |                           scipy._lib.array_api_extra._lib._at
import time:       235 |       1237 |                         scipy._lib.array_api_extra._lib._funcs
import time:       146 |       1435 |                       scipy._lib.array_api_extra._delegation
import time:       171 |        171 |                       scipy._lib.array_api_extra._lib._lazy
import time:       123 |       1729 |                     scipy._lib.array_api_extra
import time:       268 |       2156 |                   scipy.optimize._numdiff
import time:       193 |        193 |                     scipy.optimize._hessian_update_strategy
import time:       288 |        480 |                   scipy.optimize._differentiable_functions
import time:      1786 |      13555 |                 scipy.optimize._optimize
import time:       150 |        150 |                     scipy.optimize._trustregion
import time:       112 |        262 |                   scipy.optimize._trustregion_dogleg
import time:       123 |        123 |                   scipy.optimize._trustregion_ncg
import time:       148 |        148 |                         scipy._lib.messagestream
import time:       267 |        415 |                       scipy.optimize._trlib._trlib
import time:        65 |        479 |                     scipy.optimize._trlib
import time:        65 |        544 |                   scipy.optimize._trustregion_krylov
import time:       129 |        129 |                   scipy.optimize._trustregion_exact
import time:       344 |        344 |                       scipy.optimize._constraints
import time:        75 |         75 |                             sksparse
import time:        26 |        100 |                           sksparse.cholmod
import time:       121 |        220 |                         scipy.optimize._trustregion_constr.projections
import time:       133 |        133 |                         scipy.optimize._trustregion_constr.qp_subproblem
import time:       142 |        493 |                       scipy.optimize._trustregion_constr.equality_constrained_sqp
import time:       155 |        155 |                       scipy.optimize._trustregion_constr.canonical_constraint
import time:       130 |        130 |                       scipy.optimize._trustregion_constr.tr_interior_point
import time:       102 |        102 |                       scipy.optimize._trustregion_constr.report
import time:       302 |       1524 |                     scipy.optimize._trustregion_constr.minimize_trustregion_constr
import time:        66 |       1590 |                   scipy.optimize._trustregion_constr
import time:       346 |        346 |                     scipy.optimize._lbfgsb
import time:       178 |        523 |                   scipy.optimize._lbfgsb_py
import time:       150 |        150 |                     scipy.optimize._moduleTNC
import time:       167 |        316 |                   scipy.optimize._tnc
import time:       103 |        103 |                   scipy.optimize._cobyla_py
import time:        60 |         60 |                   scipy.optimize._cobyqa_py
import time:       309 |        309 |                     scipy.optimize._slsqplib
import time:       161 |        470 |                   scipy.optimize._slsqp_py
import time:       306 |       4420 |                 scipy.optimize._minimize
import time:       116 |        116 |                     scipy.optimize._minpack
import time:       168 |        168 |                           scipy.optimize._lsq.common
import time:       135 |        302 |                         scipy.optimize._lsq.trf
import time:       158 |        158 |                         scipy.optimize._lsq.dogbox
import time:       211 |        670 |                       scipy.optimize._lsq.least_squares
import time:       136 |        136 |                           scipy.optimize._lsq.givens_elimination
import time:       105 |        241 |                         scipy.optimize._lsq.trf_linear
import time:        86 |         86 |                         scipy.optimize._lsq.bvls
import time:       111 |        436 |                       scipy.optimize._lsq.lsq_linear
import time:        76 |       1181 |                     scipy.optimize._lsq
import time:       207 |       1504 |                   scipy.optimize._minpack_py
import time:       110 |        110 |                   scipy.optimize._spectral
import time:      1217 |       1217 |                   scipy.optimize._nonlin
import time:       198 |       3026 |                 scipy.optimize._root
import time:       108 |        108 |                     scipy.optimize._zeros
import time:       267 |        375 |                   scipy.optimize._zeros_py
import time:       142 |        516 |                 scipy.optimize._root_scalar
import time:       384 |        384 |                 scipy.optimize._nnls
import time:      1119 |       1119 |                 scipy.optimize._basinhopping
import time:        58 |         58 |                       scipy.optimize._highspy
import time:      2871 |       2871 |                       scipy.optimize._highspy._core
import time:       293 |        293 |                       scipy.optimize._highspy._highs_options
import time:       272 |       3493 |                     scipy.optimize._highspy._highs_wrapper
import time:       123 |       3615 |                   scipy.optimize._linprog_highs
import time:        90 |         90 |                                   uarray
import time:       273 |        273 |                                       scipy._lib._uarray._uarray
import time:       336 |        608 |                                     scipy._lib._uarray._backend
import time:       205 |        813 |                                   scipy._lib._uarray
import time:       142 |       1044 |                                 scipy._lib.uarray
import time:       590 |       1634 |                               scipy.fft._basic
import time:       284 |        284 |                               scipy.fft._realtransforms
import time:       135 |        135 |                                     scipy.special._sf_error
import time:       255 |        255 |                                       scipy.special._ufuncs_cxx
import time:       192 |        192 |                                       scipy.special._ellip_harm_2
import time:       427 |        427 |                                       scipy.special._special_ufuncs
import time:       300 |        300 |                                       scipy.special._gufuncs
import time:       725 |       1897 |                                     scipy.special._ufuncs
import time:      5259 |       5259 |                                     scipy.special._support_alternative_backends
import time:        76 |         76 |                                       scipy.special._input_validation
import time:       465 |        465 |                                       scipy.special._specfun
import time:       144 |        144 |                                       scipy.special._comb
import time:       328 |        328 |                                       scipy.special._multiufuncs
import time:       526 |       1538 |                                     scipy.special._basic
import time:       895 |        895 |                                     scipy.special._logsumexp
import time:       338 |        338 |                                     scipy.special._orthogonal
import time:        84 |         84 |                                     scipy.special._spfun_stats
import time:        93 |         93 |                                     scipy.special._ellip_harm
import time:        58 |         58 |                                     scipy.special._lambertw
import time:        96 |         96 |                                     scipy.special._spherical_bessel
import time:        59 |         59 |                                     scipy.special.add_newdocs
import time:        96 |         96 |                                     scipy.special.basic
import time:       102 |        102 |                                     scipy.special.orthogonal
import time:        50 |         50 |                                     scipy.special.specfun
import time:        46 |         46 |                                     scipy.special.sf_error
import time:        44 |         44 |                                     scipy.special.spfun_stats
import time:       516 |      11296 |                                   scipy.special
import time:       158 |      11453 |                                 scipy.fft._fftlog_backend
import time:       180 |      11633 |                               scipy.fft._fftlog
import time:       335 |        335 |                                     scipy.fft._pocketfft.pypocketfft
import time:       159 |        159 |                                     scipy.fft._pocketfft.helper
import time:       233 |        726 |                                   scipy.fft._pocketfft.basic
import time:       149 |        149 |                                   scipy.fft._pocketfft.realtransforms
import time:       161 |       1036 |                                 scipy.fft._pocketfft
import time:       210 |       1246 |                               scipy.fft._helper
import time:       213 |        213 |                                 scipy.fft._basic_backend
import time:       162 |        162 |                                 scipy.fft._realtransforms_backend
import time:       156 |        530 |                               scipy.fft._backend
import time:       163 |      15488 |                             scipy.fft
import time:       284 |      15771 |                           scipy.linalg._decomp_interpolative
import time:       108 |      15879 |                         scipy.linalg.interpolative
import time:       118 |      15997 |                       scipy.optimize._remove_redundancy
import time:       347 |      16343 |                     scipy.optimize._linprog_util
import time:        61 |         61 |                     sksparse
import time:        35 |         35 |                       scikits
import time:        16 |         50 |                     scikits.umfpack
import time:       218 |      16670 |                   scipy.optimize._linprog_ip
import time:       151 |        151 |                   scipy.optimize._linprog_simplex
import time:       286 |        286 |                     scipy.optimize._bglu_dense
import time:       165 |        450 |                   scipy.optimize._linprog_rs
import time:       127 |        127 |                   scipy.optimize._linprog_doc
import time:       198 |      21209 |                 scipy.optimize._linprog
import time:       120 |        120 |                 scipy.optimize._lsap
import time:      1600 |       1600 |                 scipy.optimize._differentialevolution
import time:       209 |        209 |                   scipy.optimize._pava_pybind
import time:       124 |        333 |                 scipy.optimize._isotonic
import time:       402 |        402 |                     scipy.spatial._ckdtree
import time:       582 |        984 |                   scipy.spatial._kdtree
import time:       457 |        457 |                   scipy.spatial._qhull
import time:       148 |        148 |                     scipy.spatial._voronoi
import time:       190 |        338 |                   scipy.spatial._spherical_voronoi
import time:       457 |        457 |                   scipy.spatial._plotutils
import time:       120 |        120 |                   scipy.spatial._procrustes
import time:       166 |        166 |                       scipy.spatial._hausdorff
import time:       361 |        361 |                       scipy.spatial._distance_pybind
import time:       149 |        149 |                       scipy.spatial._distance_wrap
import time:      1928 |       2602 |                     scipy.spatial.distance
import time:       180 |       2782 |                   scipy.spatial._geometric_slerp
import time:       100 |        100 |                   scipy.spatial.ckdtree
import time:        59 |         59 |                   scipy.spatial.kdtree
import time:        48 |         48 |                   scipy.spatial.qhull
import time:      3062 |       3062 |                             scipy.constants._codata
import time:       950 |        950 |                             scipy.constants._constants
import time:       163 |        163 |                             scipy.constants.codata
import time:       199 |        199 |                             scipy.constants.constants
import time:       672 |       5045 |                           scipy.constants
import time:       117 |       5162 |                         scipy.spatial.transform._rotation_groups
import time:       671 |       5832 |                       scipy.spatial.transform._rotation
import time:       352 |       6184 |                     scipy.spatial.transform._rigid_transform
import time:       158 |        158 |                     scipy.spatial.transform._rotation_spline
import time:        62 |         62 |                     scipy.spatial.transform.rotation
import time:       110 |       6512 |                   scipy.spatial.transform
import time:        83 |         83 |                     scipy.optimize._shgo_lib
import time:       257 |        257 |                     scipy.optimize._shgo_lib._vertex
import time:       589 |        928 |                   scipy.optimize._shgo_lib._complex
import time:       587 |      13367 |                 scipy.optimize._shgo
import time:       819 |        819 |                 scipy.optimize._dual_annealing
import time:       198 |        198 |                 scipy.optimize._qap
import time:       271 |        271 |                   scipy.optimize._direct
import time:       574 |        845 |                 scipy.optimize._direct_py
import time:       161 |        161 |                 scipy.optimize._milp
import time:        96 |         96 |                 scipy.optimize.cobyla
import time:        64 |         64 |                 scipy.optimize.lbfgsb
import time:        54 |         54 |                 scipy.optimize.linesearch
import time:        61 |         61 |                 scipy.optimize.minpack
import time:        59 |         59 |                 scipy.optimize.minpack2
import time:        48 |         48 |                 scipy.optimize.moduleTNC
import time:        53 |         53 |                 scipy.optimize.nonlin
import time:        55 |         55 |                 scipy.optimize.optimize
import time:        46 |         46 |                 scipy.optimize.slsqp
import time:        46 |         46 |                 scipy.optimize.tnc
import time:        47 |         47 |                 scipy.optimize.zeros
import time:       477 |      62767 |               scipy.optimize
import time:       775 |      63541 |             optimagic.parameters.bounds
import time:       313 |      63854 |           optimagic.deprecations
import time:        62 |         62 |                   _plotly_utils
import time:       117 |        178 |                 _plotly_utils.importers
import time:       153 |        331 |               plotly
import time:        69 |         69 |                 _plotly_utils.optional_imports
import time:        67 |        135 |               plotly.optional_imports
import time:       180 |        180 |                 plotly.graph_objs
import time:       130 |        130 |                   PIL._version
import time:      1105 |       1235 |                 _plotly_utils.basevalidators
import time:       310 |        310 |                   plotly.io
import time:        91 |         91 |                   plotly.express._special_inputs
import time:        99 |         99 |                   plotly.express.trendline_functions
import time:       298 |        298 |                       _plotly_utils.exceptions
import time:        89 |         89 |                         _plotly_utils.colors._swatches
import time:       154 |        154 |                         _plotly_utils.colors.colorbrewer
import time:       115 |        115 |                         _plotly_utils.colors.carto
import time:       172 |        528 |                       _plotly_utils.colors.qualitative
import time:        69 |         69 |                         _plotly_utils.colors.plotlyjs
import time:        85 |         85 |                         _plotly_utils.colors.cmocean
import time:       146 |        299 |                       _plotly_utils.colors.sequential
import time:        81 |         81 |                       _plotly_utils.colors.diverging
import time:        66 |         66 |                       _plotly_utils.colors.cyclical
import time:      2525 |       3795 |                     _plotly_utils.colors
import time:        87 |       3882 |                   plotly.colors
import time:        91 |         91 |                   packaging
import time:       101 |        101 |                     packaging._structures
import time:      2059 |       2159 |                   packaging.version
import time:       825 |        825 |                   plotly._subplots
import time:       473 |        473 |                     _plotly_utils.utils
import time:       127 |        127 |                     plotly.shapeannotation
import time:      1855 |       2453 |                   plotly.basedatatypes
import time:      3949 |      13856 |                 plotly.express._core
import time:       193 |        193 |                 plotly.express.imshow_utils
import time:       966 |        966 |                     _plotly_utils.png
import time:      2344 |       2344 |                       PIL.ExifTags
import time:        73 |         73 |                         PIL._deprecate
import time:       391 |        463 |                       PIL.ImageMode
import time:       688 |        688 |                       PIL.TiffTags
import time:       123 |        123 |                       PIL._binary
import time:       197 |        197 |                         PIL._typing
import time:       138 |        335 |                       PIL._util
import time:        63 |         63 |                       defusedxml
import time:       898 |        898 |                       PIL._imaging
import time:      1644 |       6555 |                     PIL.Image
import time:       141 |       7661 |                   _plotly_utils.data_utils
import time:       183 |       7843 |                 plotly.utils
import time:        65 |         65 |                 xarray
import time:      1519 |      24888 |               plotly.express._imshow
import time:       153 |        153 |                 plotly.express._doc
import time:     35330 |      35482 |               plotly.express._chart_types
import time:       165 |        165 |                 plotly.data
import time:       183 |        347 |               plotly.express.data
import time:        98 |         98 |               plotly.express.colors
import time:       451 |      61729 |             plotly.express
import time:        64 |         64 |             petsc4py
import time:        38 |         38 |             nlopt
import time:        32 |         32 |             pybobyqa
import time:        32 |         32 |             dfols
import time:        30 |         30 |             pygmo
import time:        30 |         30 |             cyipopt
import time:        29 |         29 |             fides
import time:        30 |         30 |             jax
import time:        30 |         30 |             tranquilo
import time:        31 |         31 |             numba
import time:       100 |        100 |                       iminuit.pdg_format
import time:       120 |        120 |                         iminuit.warnings
import time:       177 |        296 |                       iminuit._optional_dependencies
import time:       169 |        565 |                     iminuit._repr_text
import time:       151 |        716 |                   iminuit._repr_html
import time:       120 |        120 |                     iminuit._parse_version
import time:       110 |        110 |                         quopri
import time:       200 |        200 |                           email._parseaddr
import time:       475 |        675 |                         email.utils
import time:       300 |        300 |                         email.errors
import time:       175 |        175 |                             email.quoprimime
import time:        98 |         98 |                             email.base64mime
import time:       100 |        100 |                               email.encoders
import time:       253 |        353 |                             email.charset
import time:      2650 |       3274 |                           email.header
import time:       388 |       3661 |                         email._policybase
import time:       202 |        202 |                         email._encoded_words
import time:        89 |         89 |                         email.iterators
import time:       542 |       5577 |                       email.message
import time:       130 |        130 |                       importlib.metadata._text
import time:       190 |       5896 |                     importlib.metadata._adapters
import time:       407 |        407 |                       email.feedparser
import time:       143 |        549 |                     email.parser
import time:       697 |       7261 |                   iminuit._deprecated
import time:      1154 |       1154 |                   iminuit.typing
import time:       770 |       9899 |                 iminuit.util
import time:      1024 |       1024 |                 iminuit._core
import time:       954 |      11876 |               iminuit.minuit
import time:       137 |        137 |               iminuit.minimize
import time:       530 |      12542 |             iminuit
import time:       194 |        194 |             nevergrad
import time:       174 |        174 |                   scipy.stats._warnings_errors
import time:       177 |        177 |                         scipy._lib.doccer
import time:       160 |        160 |                         scipy.stats._distr_params
import time:       488 |        488 |                         scipy.integrate._quadrature
import time:       473 |        473 |                           scipy.integrate._odepack
import time:       135 |        608 |                         scipy.integrate._odepack_py
import time:       172 |        172 |                           scipy.integrate._quadpack
import time:       242 |        413 |                         scipy.integrate._quadpack_py
import time:       414 |        414 |                           scipy.integrate._vode
import time:       171 |        171 |                           scipy.integrate._dop
import time:       398 |        398 |                           scipy.integrate._lsoda
import time:       645 |       1627 |                         scipy.integrate._ode
import time:       242 |        242 |                         scipy.integrate._bvp
import time:       176 |        176 |                               scipy.integrate._ivp.common
import time:       167 |        167 |                               scipy.integrate._ivp.base
import time:       352 |        694 |                             scipy.integrate._ivp.bdf
import time:       336 |        336 |                             scipy.integrate._ivp.radau
import time:       118 |        118 |                               scipy.integrate._ivp.dop853_coefficients
import time:       487 |        604 |                             scipy.integrate._ivp.rk
import time:       233 |        233 |                             scipy.integrate._ivp.lsoda
import time:       235 |       2099 |                           scipy.integrate._ivp.ivp
import time:        97 |       2195 |                         scipy.integrate._ivp
import time:       221 |        221 |                         scipy.integrate._quad_vec
import time:       151 |        151 |                           scipy._lib._elementwise_iterative_method
import time:       271 |        421 |                         scipy.integrate._tanhsinh
import time:       245 |        245 |                             scipy.integrate._rules._base
import time:       156 |        156 |                             scipy.integrate._rules._genz_malik
import time:        91 |         91 |                               scipy.integrate._rules._gauss_legendre
import time:       257 |        348 |                             scipy.integrate._rules._gauss_kronrod
import time:       141 |        889 |                           scipy.integrate._rules
import time:      1056 |       1944 |                         scipy.integrate._cubature
import time:       300 |        300 |                         scipy.integrate._lebedev
import time:        98 |         98 |                         scipy.integrate.dop
import time:        56 |         56 |                         scipy.integrate.lsoda
import time:       239 |        239 |                         scipy.integrate.vode
import time:        52 |         52 |                         scipy.integrate.odepack
import time:        49 |         49 |                         scipy.integrate.quadpack
import time:        92 |         92 |                         scipy.stats._finite_differences
import time:        91 |         91 |                         scipy.stats._constants
import time:       117 |        117 |                         scipy.stats._censored_data
import time:      1298 |      10881 |                       scipy.stats._distn_infrastructure
import time:       186 |        186 |                                 scipy.interpolate._fitpack
import time:       250 |        250 |                                 scipy.interpolate._dfitpack
import time:       423 |        858 |                               scipy.interpolate._fitpack_impl
import time:       173 |        173 |                                 scipy.interpolate._dierckx
import time:       485 |        658 |                               scipy.interpolate._bsplines
import time:       133 |       1647 |                             scipy.interpolate._fitpack_py
import time:       223 |        223 |                             scipy.interpolate._polyint
import time:       218 |        218 |                             scipy.interpolate._ppoly
import time:       253 |        253 |                             scipy.interpolate._interpnd
import time:       476 |       2816 |                           scipy.interpolate._interpolate
import time:       365 |        365 |                           scipy.interpolate._fitpack2
import time:       152 |        152 |                           scipy.interpolate._rbf
import time:       178 |        178 |                             scipy.interpolate._rbfinterp_pythran
import time:       152 |        329 |                           scipy.interpolate._rbfinterp
import time:       228 |        228 |                           scipy.interpolate._cubic
import time:        94 |         94 |                           scipy.interpolate._ndgriddata
import time:       170 |        170 |                           scipy.interpolate._fitpack_repro
import time:        90 |         90 |                           scipy.interpolate._pade
import time:       237 |        237 |                             scipy.interpolate._rgi_cython
import time:       402 |        402 |                             scipy.interpolate._ndbspline
import time:       243 |        881 |                           scipy.interpolate._rgi
import time:       214 |        214 |                           scipy.interpolate._bary_rational
import time:       103 |        103 |                           scipy.interpolate.fitpack
import time:        59 |         59 |                           scipy.interpolate.fitpack2
import time:        52 |         52 |                           scipy.interpolate.interpolate
import time:        50 |         50 |                           scipy.interpolate.ndgriddata
import time:        68 |         68 |                           scipy.interpolate.polyint
import time:        53 |         53 |                           scipy.interpolate.rbf
import time:        61 |         61 |                           scipy.interpolate.interpnd
import time:       350 |       6128 |                         scipy.interpolate
import time:       953 |        953 |                           scipy.special.cython_special
import time:      2311 |       3263 |                         scipy.stats._stats
import time:       368 |        368 |                         scipy.stats._tukeylambda_stats
import time:       241 |        241 |                         scipy.stats._ksstats
import time:     37893 |      47891 |                       scipy.stats._continuous_distns
import time:       364 |        364 |                         scipy.stats._biasedurn
import time:       199 |        199 |                         scipy.stats._stats_pythran
import time:      6296 |       6858 |                       scipy.stats._discrete_distns
import time:       246 |        246 |                         scipy.stats._levy_stable.levyst
import time:      1219 |       1464 |                       scipy.stats._levy_stable
import time:       201 |        201 |                         scipy.stats._axis_nan_policy
import time:       921 |       1121 |                       scipy.stats._entropy
import time:       332 |      68545 |                     scipy.stats.distributions
import time:       102 |        102 |                       scipy._lib._bunch
import time:      1073 |       1073 |                       scipy.stats._stats_mstats_common
import time:      1253 |       2427 |                     scipy.stats._mstats_basic
import time:       260 |        260 |                       scipy.stats._common
import time:      2034 |       2294 |                     scipy.stats._hypotests
import time:      3562 |       3562 |                     scipy.stats._resampling
import time:       180 |        180 |                     scipy.stats._binomtest
import time:     23283 |     100289 |                   scipy.stats._stats_py
import time:       481 |        481 |                   scipy.stats._variation
import time:       309 |        309 |                     scipy.stats._ansari_swilk_statistics
import time:       224 |        224 |                     scipy.stats._wilcoxon
import time:      1132 |       1132 |                     scipy.stats._fit
import time:       507 |        507 |                       scipy.stats._relative_risk
import time:       301 |        301 |                       scipy.stats._crosstab
import time:       159 |        159 |                       scipy.stats._odds_ratio
import time:       383 |       1349 |                     scipy.stats.contingency
import time:      5588 |       8599 |                   scipy.stats._morestats
import time:       155 |        155 |                         scipy.sparse.csgraph._laplacian
import time:       216 |        216 |                             scipy.sparse.csgraph._tools
import time:       113 |        329 |                           scipy.sparse.csgraph._validation
import time:       406 |        734 |                         scipy.sparse.csgraph._shortest_path
import time:       224 |        224 |                         scipy.sparse.csgraph._traversal
import time:       177 |        177 |                         scipy.sparse.csgraph._min_spanning_tree
import time:       225 |        225 |                         scipy.sparse.csgraph._flow
import time:       211 |        211 |                         scipy.sparse.csgraph._matching
import time:       214 |        214 |                         scipy.sparse.csgraph._reordering
import time:       203 |       2140 |                       scipy.sparse.csgraph
import time:       226 |        226 |                       scipy.stats._sobol
import time:       250 |        250 |                       scipy.stats._qmc_cy
import time:       958 |       3573 |                     scipy.stats._qmc
import time:       732 |       4304 |                   scipy.stats._multicomp
import time:       349 |        349 |                   scipy.stats._binned_statistic
import time:       224 |        224 |                       scipy.stats._covariance
import time:       216 |        216 |                         scipy.stats._rcont.rcont
import time:       310 |        526 |                       scipy.stats._rcont
import time:       192 |        192 |                         scipy.stats._qmvnt_cy
import time:       178 |        369 |                       scipy.stats._qmvnt
import time:      2129 |       3246 |                     scipy.stats._multivariate
import time:       261 |       3507 |                   scipy.stats._kde
import time:       323 |        323 |                     scipy.stats._mstats_extras
import time:       166 |        488 |                   scipy.stats.mstats
import time:        67 |         67 |                   scipy.stats.qmc
import time:       503 |        503 |                   scipy.stats._page_trend_test
import time:       837 |        837 |                   scipy.stats._mannwhitneyu
import time:       153 |        153 |                   scipy.stats._bws_test
import time:       862 |        862 |                   scipy.stats._sensitivity_analysis
import time:       963 |        963 |                   scipy.stats._survival
import time:       151 |        151 |                     scipy.optimize._bracket
import time:       188 |        188 |                     scipy.optimize._chandrupatla
import time:       146 |        146 |                     scipy.stats._probability_distribution
import time:      1868 |       2352 |                   scipy.stats._distribution_infrastructure
import time:      7928 |       7928 |                   scipy.stats._new_distributions
import time:        77 |         77 |                             scipy.ndimage._ni_support
import time:       213 |        213 |                             scipy.ndimage._nd_image
import time:       155 |        155 |                             scipy.ndimage._ni_docstrings
import time:       140 |        140 |                             scipy.ndimage._rank_filter_1d
import time:      1152 |       1735 |                           scipy.ndimage._filters
import time:       121 |        121 |                           scipy.ndimage._fourier
import time:       405 |        405 |                           scipy.ndimage._interpolation
import time:       235 |        235 |                             scipy.ndimage._ni_label
import time:       256 |        256 |                             scipy.ndimage._morphology
import time:       335 |        826 |                           scipy.ndimage._measurements
import time:       143 |       3228 |                         scipy.ndimage._ndimage_api
import time:       150 |        150 |                         scipy.ndimage._delegators
import time:       199 |       3575 |                       scipy.ndimage._support_alternative_backends
import time:        69 |         69 |                       scipy.ndimage.filters
import time:        52 |         52 |                       scipy.ndimage.fourier
import time:        52 |         52 |                       scipy.ndimage.interpolation
import time:        53 |         53 |                       scipy.ndimage.measurements
import time:        55 |         55 |                       scipy.ndimage.morphology
import time:       212 |       4065 |                     scipy.ndimage
import time:       473 |       4537 |                   scipy.stats._mgc
import time:       527 |        527 |                   scipy.stats._correlation
import time:       122 |        122 |                   scipy.stats._quantile
import time:        64 |         64 |                   scipy.stats.biasedurn
import time:        54 |         54 |                   scipy.stats.kde
import time:        66 |         66 |                   scipy.stats.morestats
import time:        77 |         77 |                   scipy.stats.mstats_basic
import time:        71 |         71 |                   scipy.stats.mstats_extras
import time:        59 |         59 |                   scipy.stats.mvn
import time:        75 |         75 |                   scipy.stats.stats
import time:       770 |     138264 |                 scipy.stats
import time:       142 |        142 |                     sklearn._config
import time:       178 |        178 |                       sklearn.__check_build._check_build
import time:        82 |        259 |                     sklearn.__check_build
import time:        58 |         58 |                     sklearn._distributor_init
import time:       183 |        183 |                       sklearn.exceptions
import time:        82 |         82 |                               sklearn.utils._bunch
import time:       487 |        568 |                             sklearn.utils._metadata_requests
import time:       139 |        706 |                           sklearn.utils.metadata_routing
import time:       168 |        168 |                                   sklearn.externals
import time:       188 |        188 |                                       sklearn.externals.array_api_compat.common._helpers
import time:       124 |        312 |                                     sklearn.externals.array_api_compat.common
import time:       240 |        551 |                                   sklearn.externals.array_api_compat
import time:        53 |         53 |                                           sklearn.externals.array_api_extra._lib._utils
import time:        57 |         57 |                                             sklearn.externals._array_api_compat_vendor
import time:       104 |        160 |                                           sklearn.externals.array_api_extra._lib._utils._compat
import time:       256 |        469 |                                         sklearn.externals.array_api_extra._lib._backends
import time:        68 |        536 |                                       sklearn.externals.array_api_extra._lib
import time:        76 |         76 |                                             sklearn.externals.array_api_extra._lib._utils._typing
import time:       111 |        187 |                                           sklearn.externals.array_api_extra._lib._utils._helpers
import time:       281 |        467 |                                         sklearn.externals.array_api_extra._lib._at
import time:       226 |        692 |                                       sklearn.externals.array_api_extra._lib._funcs
import time:       131 |       1358 |                                     sklearn.externals.array_api_extra._delegation
import time:       117 |        117 |                                     sklearn.externals.array_api_extra._lib._lazy
import time:       221 |       1696 |                                   sklearn.externals.array_api_extra
import time:       517 |        517 |                                       sklearn.externals.array_api_compat.common._aliases
import time:       105 |        105 |                                       sklearn.externals.array_api_compat._internal
import time:       110 |        110 |                                       sklearn.externals.array_api_compat.numpy._info
import time:       692 |       1422 |                                     sklearn.externals.array_api_compat.numpy._aliases
import time:       674 |        674 |                                       sklearn.externals.array_api_compat.common._linalg
import time:       372 |       1046 |                                     sklearn.externals.array_api_compat.numpy.linalg
import time:        80 |         80 |                                       sklearn.externals.array_api_compat.common._fft
import time:       300 |        380 |                                     sklearn.externals.array_api_compat.numpy.fft
import time:       436 |       3282 |                                   sklearn.externals.array_api_compat.numpy
import time:        70 |         70 |                                       sklearn.externals._packaging
import time:        94 |         94 |                                       sklearn.externals._packaging._structures
import time:       681 |        844 |                                     sklearn.externals._packaging.version
import time:       148 |        148 |                                         ctypes.util
import time:       588 |        735 |                                       threadpoolctl
import time:       189 |        924 |                                     sklearn.utils.parallel
import time:       515 |       2282 |                                   sklearn.utils.fixes
import time:       388 |       8365 |                                 sklearn.utils._array_api
import time:       116 |        116 |                                 sklearn.utils.deprecation
import time:       494 |        494 |                                 sklearn.utils._isfinite
import time:      2927 |       2927 |                                 sklearn.utils._tags
import time:      4452 |      16352 |                               sklearn.utils.validation
import time:       505 |      16857 |                             sklearn.utils._param_validation
import time:       138 |      16994 |                           sklearn.utils._chunking
import time:       366 |        366 |                               sklearn.utils.sparsefuncs_fast
import time:       226 |        591 |                             sklearn.utils.extmath
import time:       188 |        779 |                           sklearn.utils._indexing
import time:        60 |         60 |                             sklearn.utils._missing
import time:       139 |        198 |                           sklearn.utils._mask
import time:        65 |         65 |                             sklearn.utils._repr_html
import time:       151 |        216 |                           sklearn.utils._repr_html.base
import time:       704 |        704 |                               html.entities
import time:       275 |        978 |                             html
import time:       267 |       1245 |                           sklearn.utils._repr_html.estimator
import time:       227 |        227 |                           sklearn.utils.class_weight
import time:       174 |        174 |                           sklearn.utils.discovery
import time:       266 |        266 |                           sklearn.utils.murmurhash
import time:       254 |      21055 |                         sklearn.utils
import time:        22 |      21076 |                       sklearn.utils._metadata_requests
import time:       124 |        124 |                       sklearn.utils._repr_html.params
import time:       115 |        115 |                         sklearn.utils._available_if
import time:       241 |        355 |                       sklearn.utils._set_output
import time:       451 |      22187 |                     sklearn.base
import time:       277 |        277 |                       sklearn.utils._openmp_helpers
import time:       154 |        430 |                     sklearn.utils._show_versions
import time:        97 |         97 |                     sklearn._built_with_meson
import time:       222 |      23392 |                   sklearn
import time:       318 |        318 |                           sklearn.metrics.cluster._bicluster
import time:        72 |         72 |                               sklearn.utils._unique
import time:       147 |        219 |                             sklearn.utils.multiclass
import time:       273 |        273 |                             sklearn.metrics.cluster._expected_mutual_info_fast
import time:       263 |        755 |                           sklearn.metrics.cluster._supervised
import time:       249 |        249 |                                 sklearn.utils.sparsefuncs
import time:       285 |        285 |                                   sklearn.utils._encode
import time:       551 |        835 |                                 sklearn.preprocessing._encoders
import time:      2686 |       3769 |                               sklearn.preprocessing._data
import time:       166 |        166 |                                 sklearn.utils.stats
import time:       712 |        878 |                               sklearn.preprocessing._discretization
import time:       271 |        271 |                                 sklearn.utils.metaestimators
import time:       646 |        917 |                               sklearn.preprocessing._function_transformer
import time:       926 |        926 |                               sklearn.preprocessing._label
import time:       333 |        333 |                                 sklearn.preprocessing._csr_polynomial_expansion
import time:       796 |       1129 |                               sklearn.preprocessing._polynomial
import time:       387 |        387 |                                 sklearn.preprocessing._target_encoder_fast
import time:       474 |        860 |                               sklearn.preprocessing._target_encoder
import time:       189 |       8665 |                             sklearn.preprocessing
import time:       467 |        467 |                                   sklearn.metrics._dist_metrics
import time:       450 |        450 |                                       sklearn.metrics._pairwise_distances_reduction._datasets_pair
import time:       538 |        538 |                                       sklearn.utils._cython_blas
import time:       390 |       1377 |                                     sklearn.metrics._pairwise_distances_reduction._base
import time:       320 |        320 |                                     sklearn.metrics._pairwise_distances_reduction._middle_term_computer
import time:       155 |        155 |                                     sklearn.utils._heap
import time:       109 |        109 |                                     sklearn.utils._sorting
import time:       452 |       2410 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin
import time:       317 |        317 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin_classmode
import time:       175 |        175 |                                     sklearn.utils._vector_sentinel
import time:       362 |        537 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors
import time:       284 |        284 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors_classmode
import time:       344 |       4357 |                                 sklearn.metrics._pairwise_distances_reduction._dispatcher
import time:       104 |       4461 |                               sklearn.metrics._pairwise_distances_reduction
import time:       464 |        464 |                               sklearn.metrics._pairwise_fast
import time:       564 |       5487 |                             sklearn.metrics.pairwise
import time:       212 |      14363 |                           sklearn.metrics.cluster._unsupervised
import time:       221 |      15655 |                         sklearn.metrics.cluster
import time:       594 |        594 |                         sklearn.metrics._classification
import time:       108 |        108 |                           sklearn.metrics._plot
import time:        61 |         61 |                           sklearn.utils._optional_dependencies
import time:        74 |         74 |                             sklearn.utils._response
import time:       217 |        291 |                           sklearn.utils._plotting
import time:       206 |        664 |                         sklearn.metrics._plot.confusion_matrix
import time:        82 |         82 |                             sklearn.metrics._base
import time:       279 |        360 |                           sklearn.metrics._ranking
import time:       131 |        491 |                         sklearn.metrics._plot.det_curve
import time:       115 |        115 |                         sklearn.metrics._plot.precision_recall_curve
import time:        96 |         96 |                         sklearn.metrics._plot.regression
import time:       120 |        120 |                         sklearn.metrics._plot.roc_curve
import time:       240 |        240 |                         sklearn.metrics._regression
import time:      2146 |       2146 |                         sklearn.metrics._scorer
import time:       244 |      20360 |                       sklearn.metrics
import time:        18 |      20377 |                     sklearn.metrics.pairwise
import time:       697 |      21074 |                   sklearn.gaussian_process.kernels
import time:       733 |        733 |                     sklearn.multiclass
import time:       195 |        195 |                     sklearn.utils.optimize
import time:       609 |       1536 |                   sklearn.gaussian_process._gpc
import time:       272 |        272 |                   sklearn.gaussian_process._gpr
import time:       123 |      46395 |                 sklearn.gaussian_process
import time:       145 |        145 |                 bayes_opt.exception
import time:       180 |        180 |                         colorama.ansi
import time:        51 |         51 |                           msvcrt
import time:        96 |         96 |                           colorama.win32
import time:       168 |        314 |                         colorama.winterm
import time:       310 |        803 |                       colorama.ansitowin32
import time:       128 |        931 |                     colorama.initialise
import time:       123 |       1054 |                   colorama
import time:        54 |         54 |                       bayes_opt.util
import time:       229 |        282 |                     bayes_opt.parameter
import time:       162 |        444 |                   bayes_opt.constraint
import time:       198 |       1695 |                 bayes_opt.target_space
import time:       357 |     186854 |               bayes_opt.acquisition
import time:       136 |        136 |                 bayes_opt.domain_reduction
import time:       120 |        120 |                 bayes_opt.logger
import time:       233 |        488 |               bayes_opt.bayesian_optimization
import time:       579 |     187921 |             bayes_opt
import time:       337 |     263062 |           optimagic.config
import time:        96 |         96 |           optimagic.decorators
import time:       270 |     342353 |         optimagic.batch_evaluators
import time:        66 |         66 |           optimagic.differentiation
import time:       202 |        202 |           optimagic.differentiation.finite_differences
import time:       197 |        197 |           optimagic.differentiation.generate_steps
import time:        93 |         93 |           optimagic.differentiation.richardson_extrapolation
import time:       327 |        327 |           optimagic.parameters.block_trees
import time:      1128 |       2011 |         optimagic.differentiation.derivatives
import time:       829 |        829 |         optimagic.differentiation.numdiff_options
import time:       126 |        126 |           optimagic.parameters.process_selectors
import time:       525 |        525 |             optimagic.parameters.scaling
import time:       114 |        114 |               optimagic.parameters.kernel_transformations
import time:        87 |         87 |                 optimagic.parameters.check_constraints
import time:       149 |        149 |                 optimagic.parameters.consolidate_constraints
import time:       148 |        382 |               optimagic.parameters.process_constraints
import time:      1167 |       1662 |             optimagic.parameters.space_conversion
import time:       576 |       2763 |           optimagic.parameters.scale_conversion
import time:       392 |        392 |           optimagic.parameters.tree_conversion
import time:       618 |       3897 |         optimagic.parameters.conversion
import time:      1039 |     350126 |       optimagic.optimization.internal_optimization_problem
import time:       145 |        145 |       optimagic.type_conversion
import time:      2010 |     583650 |     optimagic.optimization.algorithm
import time:       220 |     583869 |   optimagic.mark
import time:        98 |         98 |       optimagic.optimizers
import time:      1199 |       1297 |     optimagic.optimizers.bayesian_optimizer
import time:       581 |        581 |     optimagic.optimizers.bhhh
import time:      1096 |       1096 |     optimagic.optimizers.fides
import time:       625 |        625 |     optimagic.optimizers.iminuit_migrad
import time:       140 |        140 |         optimagic.parameters.nonlinear_constraints
import time:     11472 |      11611 |       optimagic.optimizers.scipy_optimizers
import time:      6413 |      18024 |     optimagic.optimizers.ipopt
import time:      2604 |       2604 |     optimagic.optimizers.nag_optimizers
import time:       776 |        776 |     optimagic.optimizers.neldermead
import time:       760 |        760 |     optimagic.optimizers.nevergrad_optimizers
import time:      7719 |       7719 |     optimagic.optimizers.nlopt_optimizers
import time:        77 |         77 |         optimagic.optimizers._pounders
import time:        57 |         57 |           optimagic.optimizers._pounders._conjugate_gradient
import time:        71 |         71 |           optimagic.optimizers._pounders._steihaug_toint
import time:        87 |         87 |           optimagic.optimizers._pounders._trsbox
import time:       331 |        546 |         optimagic.optimizers._pounders.bntr
import time:       356 |        356 |         optimagic.optimizers._pounders.gqtpar
import time:       360 |       1338 |       optimagic.optimizers._pounders.pounders_auxiliary
import time:       128 |        128 |       optimagic.optimizers._pounders.pounders_history
import time:      1269 |       2733 |     optimagic.optimizers.pounders
import time:        55 |         55 |       pygmo
import time:     12106 |      12161 |     optimagic.optimizers.pygmo_optimizers
import time:        64 |         64 |       petsc4py
import time:       889 |        953 |     optimagic.optimizers.tao_optimizers
import time:      3585 |       3585 |     optimagic.optimizers.tranquilo
import time:    120204 |     173110 |   optimagic.algorithms
import time:       166 |        166 |     optimagic.benchmarking
import time:        99 |         99 |     optimagic.benchmarking.process_benchmark_results
import time:        98 |         98 |       optimagic.visualization
import time:       137 |        234 |     optimagic.visualization.profile_plot
import time:       247 |        745 |   optimagic.benchmarking.benchmark_reports
import time:      1884 |       1884 |       optimagic.benchmarking.more_wild
import time:      1097 |       2980 |     optimagic.benchmarking.cartis_roberts
import time:       139 |        139 |     optimagic.benchmarking.noise_distributions
import time:        74 |         74 |       optimagic.shared
import time:       166 |        239 |     optimagic.shared.process_user_function
import time:       193 |       3550 |   optimagic.benchmarking.get_benchmark_problems
import time:      1664 |       1664 |         optimagic.optimization.multistart_options
import time:       139 |        139 |         optimagic.optimization.scipy_aliases
import time:      1092 |       2893 |       optimagic.optimization.create_optimization_problem
import time:       224 |        224 |       optimagic.optimization.error_penalty
import time:        69 |         69 |         optimagic.optimization.optimization_logging
import time:       195 |        263 |       optimagic.optimization.multistart
import time:        65 |         65 |         optimagic.shared.compat
import time:      1367 |       1432 |       optimagic.optimization.optimize_result
import time:       118 |        118 |         optimagic.optimization.convergence_report
import time:       552 |        670 |       optimagic.optimization.process_results
import time:       292 |       5772 |     optimagic.optimization.optimize
import time:       126 |       5897 |   optimagic.benchmarking.run_benchmark
import time:       326 |        326 |   optimagic.logging.read_log
import time:       125 |        125 |   optimagic.parameters.constraint_tools
import time:       164 |        164 |     plotly.graph_objects
import time:        78 |         78 |       plotly.subplots
import time:       123 |        200 |     optimagic.visualization.plotting_utilities
import time:       145 |        508 |   optimagic.visualization.convergence_plot
import time:       131 |        131 |   optimagic.visualization.history_plots
import time:       157 |        157 |   optimagic.visualization.slice_plot
import time:        91 |         91 |   optimagic._version
import time:       444 |    1040907 | optimagic
