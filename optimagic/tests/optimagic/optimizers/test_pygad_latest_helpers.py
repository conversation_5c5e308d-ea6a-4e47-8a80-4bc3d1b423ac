"""Unit tests for helper functions in the latest PyGAD optimizer."""

import warnings
from unittest.mock import Mock

import numpy as np

from optimagic.config import IS_PYGAD_INSTALLED
from optimagic.optimization.algorithm import InternalOptimizeResult

# Import pytest only if available
try:
    import pytest
    PYTEST_AVAILABLE = True
    # Skip all tests if PyGAD is not installed
    pytestmark = pytest.mark.skipif(
        not IS_PYGAD_INSTALLED, reason="PyGAD not installed"
    )
except ImportError:
    PYTEST_AVAILABLE = False
    # Create a dummy pytest module for basic functionality
    class DummyPytest:
        class mark:
            @staticmethod
            def parametrize(params, values):
                def decorator(func):
                    return func
                return decorator
            @staticmethod
            def skipif(condition, reason=""):
                def decorator(func):
                    return func
                return decorator
    pytest = DummyPytest()

if IS_PYGAD_INSTALLED:
    from optimagic.optimizers.pygad_lates_new import (
        _process_pygad_result,
        determine_effective_batch_size,
    )


class TestDetermineEffectiveBatchSize:
    """Test the determine_effective_batch_size helper function."""

    @pytest.mark.parametrize(
        "fitness_batch_size,n_cores,expected",
        [
            # Basic cases
            (None, 1, None),  # Single-threaded, no batching
            (5, 1, 5),  # Single-threaded with user-defined batch size
            (None, 4, 4),  # Auto-sizing for parallel processing
            (8, 4, 8),  # User-defined batch size with parallel processing
            # Edge cases
            (None, 0, None),  # Zero cores
            (0, 4, 0),  # Zero batch size
            (1, 1, 1),  # Minimal values
            (None, 100, 100),  # Large number of cores
        ],
    )
    def test_determine_effective_batch_size_return_values(
        self, fitness_batch_size, n_cores, expected
    ):
        """Test that determine_effective_batch_size returns correct values."""
        result = determine_effective_batch_size(fitness_batch_size, n_cores)
        assert result == expected

    @pytest.mark.parametrize(
        "fitness_batch_size,n_cores,should_warn",
        [
            # Cases that should warn
            (2, 4, True),  # Batch size smaller than cores
            (1, 8, True),  # Very small batch size
            (0, 4, True),  # Zero batch size with multiple cores
            # Cases that should not warn
            (4, 4, False),  # Equal batch size and cores
            (8, 4, False),  # Larger batch size than cores
            (None, 4, False),  # Auto-sizing
            (5, 1, False),  # Single-threaded
            (None, 1, False),  # Single-threaded auto-sizing
        ],
    )
    def test_determine_effective_batch_size_warnings(
        self, fitness_batch_size, n_cores, should_warn
    ):
        """Test warning behavior of determine_effective_batch_size."""
        if should_warn:
            warning_pattern = (
                f"fitness_batch_size \\({fitness_batch_size}\\) is smaller than "
                f"n_cores \\({n_cores}\\)"
            )
            with pytest.warns(UserWarning, match=warning_pattern):
                result = determine_effective_batch_size(fitness_batch_size, n_cores)
            assert result == fitness_batch_size
        else:
            with warnings.catch_warnings():
                warnings.simplefilter("error")  # Turn warnings into errors
                result = determine_effective_batch_size(fitness_batch_size, n_cores)
            # Should not raise any warnings

    def test_determine_effective_batch_size_type_consistency(self):
        """Test that function returns consistent types."""
        # Should return int when given int inputs
        result = determine_effective_batch_size(5, 2)
        assert isinstance(result, int)
        assert result == 5

        # Should return int when auto-sizing
        result = determine_effective_batch_size(None, 4)
        assert isinstance(result, int)
        assert result == 4

        # Should return None for single-threaded
        result = determine_effective_batch_size(None, 1)
        assert result is None


class TestProcessPygadResult:
    """Test the _process_pygad_result helper function."""

    def create_mock_ga_instance(
        self,
        best_solution=None,
        best_fitness=None,
        generations_completed=None,
        run_completed=True,
        pop_size=None,
    ):
        """Create a mock PyGAD instance for testing."""
        mock_ga = Mock()
        
        # Set default values
        if best_solution is None:
            best_solution = np.array([1.0, 2.0])
        if best_fitness is None:
            best_fitness = -5.0  # PyGAD maximizes, so negative for minimization
        if generations_completed is None:
            generations_completed = 50
        if pop_size is None:
            pop_size = [20]  # PyGAD stores as list
            
        # Configure mock methods and attributes
        mock_ga.best_solution.return_value = (best_solution, best_fitness, None)
        mock_ga.generations_completed = generations_completed
        mock_ga.run_completed = run_completed
        mock_ga.pop_size = pop_size
        
        return mock_ga

    def test_process_pygad_result_successful_run(self):
        """Test processing of successful PyGAD run."""
        mock_ga = self.create_mock_ga_instance(
            best_solution=np.array([0.1, 0.2]),
            best_fitness=-2.5,
            generations_completed=30,
            run_completed=True,
            pop_size=[15],
        )

        result = _process_pygad_result(mock_ga)

        # Check result type and basic attributes
        assert isinstance(result, InternalOptimizeResult)
        assert np.array_equal(result.x, np.array([0.1, 0.2]))
        assert result.fun == 2.5  # Should be negated from PyGAD fitness
        assert result.success is True
        assert "successfully" in result.message
        assert "30" in result.message  # Should contain generation count
        assert result.n_fun_evals == 30 * 15  # generations * population_size

    def test_process_pygad_result_failed_run(self):
        """Test processing of failed PyGAD run."""
        mock_ga = self.create_mock_ga_instance(
            best_solution=np.array([1.5, -0.5]),
            best_fitness=-10.0,
            generations_completed=25,
            run_completed=False,
            pop_size=[10],
        )

        result = _process_pygad_result(mock_ga)

        # Check result attributes for failed run
        assert isinstance(result, InternalOptimizeResult)
        assert np.array_equal(result.x, np.array([1.5, -0.5]))
        assert result.fun == 10.0  # Should be negated from PyGAD fitness
        assert result.success is False
        assert "failed" in result.message
        assert "25" in result.message  # Should contain generation count
        assert result.n_fun_evals == 25 * 10  # generations * population_size

    def test_process_pygad_result_zero_generations(self):
        """Test processing when no generations completed."""
        mock_ga = self.create_mock_ga_instance(
            best_solution=np.array([0.0, 0.0]),
            best_fitness=0.0,
            generations_completed=0,
            run_completed=False,
            pop_size=[5],
        )

        result = _process_pygad_result(mock_ga)

        assert result.n_fun_evals == 0
        assert result.success is False
        assert "0" in result.message

    def test_process_pygad_result_large_population(self):
        """Test processing with large population size."""
        mock_ga = self.create_mock_ga_instance(
            best_solution=np.array([2.0, 3.0, 4.0]),
            best_fitness=-100.0,
            generations_completed=10,
            run_completed=True,
            pop_size=[200],
        )

        result = _process_pygad_result(mock_ga)

        assert len(result.x) == 3
        assert result.fun == 100.0
        assert result.n_fun_evals == 10 * 200  # Should handle large numbers

    @pytest.mark.parametrize(
        "best_fitness,expected_fun",
        [
            (-1.0, 1.0),  # Simple negation
            (0.0, 0.0),  # Zero case
            (5.0, -5.0),  # Positive fitness (unusual but possible)
            (-1e6, 1e6),  # Large negative fitness
            (-1e-10, 1e-10),  # Very small negative fitness
        ],
    )
    def test_process_pygad_result_fitness_conversion(self, best_fitness, expected_fun):
        """Test that PyGAD fitness is correctly converted to minimization objective."""
        mock_ga = self.create_mock_ga_instance(
            best_fitness=best_fitness,
            run_completed=True,
        )

        result = _process_pygad_result(mock_ga)
        assert result.fun == expected_fun

    def test_process_pygad_result_message_content(self):
        """Test that result messages contain expected information."""
        # Test successful run message
        mock_ga_success = self.create_mock_ga_instance(
            generations_completed=42,
            run_completed=True,
        )
        result_success = _process_pygad_result(mock_ga_success)
        
        assert "Optimization terminated successfully" in result_success.message
        assert "Generations completed: 42" in result_success.message

        # Test failed run message
        mock_ga_failed = self.create_mock_ga_instance(
            generations_completed=15,
            run_completed=False,
        )
        result_failed = _process_pygad_result(mock_ga_failed)
        
        assert "Optimization failed to complete" in result_failed.message
        assert "Generations completed: 15" in result_failed.message

    def test_process_pygad_result_with_different_solution_types(self):
        """Test processing with different solution array types."""
        # Test with list input (should be converted to numpy array)
        mock_ga_list = self.create_mock_ga_instance(
            best_solution=[1.5, 2.5, 3.5],
        )
        result_list = _process_pygad_result(mock_ga_list)
        assert isinstance(result_list.x, np.ndarray)
        assert np.array_equal(result_list.x, np.array([1.5, 2.5, 3.5]))

        # Test with numpy array input
        mock_ga_array = self.create_mock_ga_instance(
            best_solution=np.array([4.0, 5.0]),
        )
        result_array = _process_pygad_result(mock_ga_array)
        assert isinstance(result_array.x, np.ndarray)
        assert np.array_equal(result_array.x, np.array([4.0, 5.0]))
