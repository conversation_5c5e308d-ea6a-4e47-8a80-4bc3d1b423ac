# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# MacOS specific service store
.DS_Store

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*build/

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec
*.sublime-workspace
*.sublime-project

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/
docs/build/
docs/source/_build/
docs/source/**/*.db
docs/source/**/*.db-shm
docs/source/**/*.db-wal
docs/source/refs.bib.bak

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# VSCode project settings
.vscode

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

*notes/

.idea/

*.bak


*.db


.pytask.sqlite3


src/estimagic/_version.py
src/optimagic/_version.py

*.~lock.*
