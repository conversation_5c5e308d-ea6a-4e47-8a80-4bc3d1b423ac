---
name: optimagic-docs
channels:
  - conda-forge
  - nodefaults
dependencies:
  - python=3.11
  - typing-extensions
  - pip
  - setuptools_scm
  - toml
  - sphinx
  - sphinxcontrib-bibtex
  - sphinx-copybutton
  - sphinx-design
  - sphinx-panels
  - ipython
  - ipython_genutils
  - myst-nb
  - furo
  - pybaum
  - matplotlib
  - seaborn
  - numpy
  - pandas
  - scipy
  - patsy
  - joblib
  - plotly>=6.2
  - nlopt
  - annotated-types
  - pygmo>=2.19.0
  - pip:
      - ../
      - Py-BOBYQA
      - DFO-LS
      - pandas-stubs  # dev, tests
      - types-cffi  # dev, tests
      - types-openpyxl  # dev, tests
      - types-jinja2  # dev, tests
      - sqlalchemy-stubs  # dev, tests
      - sphinxcontrib-mermaid  # dev, tests, docs
      - intersphinx-registry  # docs
      - fides==0.7.4  # dev, tests
