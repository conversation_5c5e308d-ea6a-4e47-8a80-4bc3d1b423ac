---
name: optimagic
channels:
  - conda-forge
  - nodefaults
dependencies:
  - python=3.10  # dev
  - cyipopt>=1.4.0  # dev, tests
  - pygmo>=2.19.0  # dev, tests, docs
  - jupyterlab  # dev, docs
  - nlopt  # dev, tests, docs
  - pip  # dev, tests, docs
  - pytest  # dev, tests
  - pytest-cov  # tests
  - pytest-xdist  # dev, tests
  - setuptools_scm  # dev
  - statsmodels  # dev, tests
  - toml  # dev
  - cloudpickle  # run, tests
  - joblib  # run, tests
  - numpy >= 2  # run, tests
  - pandas  # run, tests
  - plotly>=6.2  # run, tests
  - pybaum>=0.1.2  # run, tests
  - scipy>=1.2.1  # run, tests
  - sqlalchemy  # run, tests
  - myst-nb  # docs
  - sphinx  # docs
  - sphinx-copybutton  # docs
  - sphinx-design  # docs
  - sphinx-panels  # docs
  - sphinxcontrib-bibtex  # docs
  - intersphinx-registry  # docs
  - seaborn  # dev, tests
  - mypy=1.14.1  # dev, tests
  - pyyaml  # dev, tests
  - jinja2  # dev, tests
  - furo  # dev, docs
  - annotated-types  # dev, tests
  - iminuit  # dev, tests
  - pip:  # dev, tests, docs
      - nevergrad  # dev, tests
      - DFO-LS>=1.5.3  # dev, tests
      - Py-BOBYQA  # dev, tests
      - fides==0.7.4  # dev, tests
      - kaleido>=1.0  # dev, tests
      - pre-commit>=4  # dev
      - -e .  # dev
      # type stubs
      - pandas-stubs  # dev, tests
      - types-cffi  # dev, tests
      - types-openpyxl  # dev, tests
      - types-jinja2  # dev, tests
      - sqlalchemy-stubs  # dev, tests
      - sphinxcontrib-mermaid  # dev, tests, docs
      - pdbp  # dev
