import numpy as np
from bayes_opt import BayesianOptimization
from bayes_opt import acquisition


def f(x):
    return np.exp(-(x - 2) ** 2) + np.exp(-(x - 6) ** 2 / 10) + 1/ (x ** 2 + 1)


if __name__ == '__main__':
    acq = acquisition.UpperConfidenceBound(kappa=5)  # kappa determines explore/Exploitation ratio
    optimizer = BayesianOptimization(f=None, pbounds={'x': (-2, 2)}, verbose=2, random_state=1,
                                     allow_duplicate_points=True)
    optimizer.set_gp_params(normalize_y=True, alpha=2.5e-3, n_restarts_optimizer=20)  # tuning of the gaussian parameters...
    for point in range(20):
        next_point_to_probe = optimizer.suggest()
        NextPointValues = np.array(list(next_point_to_probe.values()))
        mean,std = optimizer._gp.predict(NextPointValues.reshape(1, -1),return_std=True)
        target = f(**next_point_to_probe)
        optimizer.register(params=next_point_to_probe, target=target)