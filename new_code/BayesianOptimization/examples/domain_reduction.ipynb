{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Sequential Domain Reduction\n", "\n", "## Background\n", "Sequential domain reduction is a process where the bounds of the optimization problem are mutated (typically contracted) to reduce the time required to converge to an optimal value. The advantage of this method is typically seen when a cost function is particularly expensive to calculate, or if the optimization routine oscillates heavily. \n", "\n", "## Basics\n", "\n", "The basic steps are a *pan* and a *zoom*. These two steps are applied at one time, therefore updating the problem search space every iteration.\n", "\n", "**Pan**: recentering the region of interest around the most optimal point found.\n", "\n", "**Zoom**: contract the region of interest.\n", "\n", "![](../docsrc/static/sdr.png)\n", "\n", "\n", "## Parameters\n", "\n", "There are three parameters for the built-in `SequentialDomainReductionTransformer` object:\n", "\n", "\n", "$\\gamma_{osc}:$ shrinkage  parameter  for  oscillation. Typically [0.5-0.7]. Default = 0.7\n", "\n", "$\\gamma_{pan}:$ panning parameter. Typically 1.0. De<PERSON>ult = 1.0\n", "\n", "$\\eta:$ zoom parameter. Default = 0.9\n", "\n", "\n", "More information can be found in this reference document:\n", "\n", "---\n", "\n", "Title: \"On the robustness of a simple domain reduction scheme for simulation‐based optimization\" \n", "\n", "Date: 2002 \n", "\n", "Author: <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, K. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "---\n", "Let's start by importing the packages we'll be needing"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from bayes_opt import BayesianOptimization\n", "from bayes_opt import SequentialDomainReductionTransformer\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's create an example cost function. This is the [Ackley function](https://en.wikipedia.org/wiki/Ackley_function), which is quite non-linear. "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def ackley(**kwargs):\n", "    x = np.fromiter(kwargs.values(), dtype=float)\n", "    arg1 = -0.2 * np.sqrt(0.5 * (x[0] ** 2 + x[1] ** 2))\n", "    arg2 = 0.5 * (np.cos(2. * np.pi * x[0]) + np.cos(2. * np.pi * x[1]))\n", "    return -1.0 * (-20. * np.exp(arg1) - np.exp(arg2) + 20. + np.e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the standard bounds for this problem."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "pbounds = {'x': (-5, 5), 'y': (-5, 5)}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "\n", "This is where we define our `bound_transformer` , the Sequential Domain Reduction Transformer\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["bounds_transformer = SequentialDomainReductionTransformer(minimum_window=0.5)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now we can set up two identical optimization problems, except one has the `bound_transformer` variable set."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["mutating_optimizer = BayesianOptimization(\n", "    f=ackley,\n", "    pbounds=pbounds,\n", "    verbose=0,\n", "    random_state=1,\n", "    bounds_transformer=bounds_transformer\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["mutating_optimizer.maximize(\n", "    init_points=2,\n", "    n_iter=50,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["standard_optimizer = BayesianOptimization(\n", "    f=ackley,\n", "    pbounds=pbounds,\n", "    verbose=0,\n", "    random_state=1,\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["standard_optimizer.maximize(\n", "    init_points=2,\n", "    n_iter=50,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["After both have completed we can plot to see how the objectives performed. It's quite obvious to see that the Sequential Domain Reduction technique contracted onto the optimal point relatively quick."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x23439267280>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(mutating_optimizer.space.target, label='Mutated Optimizer')\n", "plt.plot(standard_optimizer.space.target, label='Standard Optimizer')\n", "plt.legend()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's plot the actual contraction of one of the variables (`x`)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# example x-bound shrinking - we need to shift the x-axis by the init_points as the bounds\n", "# transformer only mutates when searching - not in the initial phase.\n", "x_min_bound = [b[0][0] for b in bounds_transformer.bounds]\n", "x_max_bound = [b[0][1] for b in bounds_transformer.bounds]\n", "x = [x[0] for x in mutating_optimizer.space.params]\n", "bounds_transformers_iteration = list(range(2, len(x)))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x23439267fd0>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(bounds_transformers_iteration, x_min_bound[1:], label='x lower bound')\n", "plt.plot(bounds_transformers_iteration, x_max_bound[1:], label='x upper bound')\n", "plt.plot(x[1:], label='x')\n", "plt.legend()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}