# This workflow will upload a Python Package using poetry when a release is created
# Note that you must manually update the version number in pyproject.toml before attempting this.

name: Upload Python Package

on:
  release:
    types: [published]

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Build and publish to pypi
      uses: JRubics/poetry-publish@v2.0
      with:
        pypi_token: ${{ secrets.PYPI_API_TOKEN }}
        # python_version: "3.10"
        # poetry_version: "==1.8" # can lock versions if we want
